-- Tạ<PERSON> bảng social_accounts để lưu trữ thông tin tài khoản mạng xã hội
CREATE TABLE IF NOT EXISTS social_accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    provider VARCHAR(50) NOT NULL,
    social_id VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    display_name VARCHAR(255),
    photo_url VARCHAR(500),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at BIGINT,
    profile_data JSONB,
    created_at BIGINT,
    updated_at BIGINT,
    tenant_id INTEGER,
    CONSTRAINT social_accounts_user_id_fk FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT social_accounts_provider_social_id_unique UNIQUE (provider, social_id)
);

-- Tạo index cho bảng social_accounts
CREATE INDEX IF NOT EXISTS social_accounts_user_id_idx ON social_accounts(user_id);
CREATE INDEX IF NOT EXISTS social_accounts_provider_idx ON social_accounts(provider);
CREATE INDEX IF NOT EXISTS social_accounts_social_id_idx ON social_accounts(social_id);
CREATE INDEX IF NOT EXISTS social_accounts_email_idx ON social_accounts(email);
CREATE INDEX IF NOT EXISTS social_accounts_tenant_id_idx ON social_accounts(tenant_id);

-- Thêm comment cho bảng và cột
COMMENT ON TABLE social_accounts IS 'Bảng lưu trữ thông tin tài khoản mạng xã hội liên kết với người dùng';
COMMENT ON COLUMN social_accounts.user_id IS 'ID của người dùng liên kết với tài khoản mạng xã hội';
COMMENT ON COLUMN social_accounts.provider IS 'Nhà cung cấp mạng xã hội (google, facebook, zalo)';
COMMENT ON COLUMN social_accounts.social_id IS 'ID của người dùng trên mạng xã hội';
COMMENT ON COLUMN social_accounts.email IS 'Email của người dùng trên mạng xã hội';
COMMENT ON COLUMN social_accounts.display_name IS 'Tên hiển thị của người dùng trên mạng xã hội';
COMMENT ON COLUMN social_accounts.photo_url IS 'URL ảnh đại diện của người dùng trên mạng xã hội';
COMMENT ON COLUMN social_accounts.access_token IS 'Access token từ mạng xã hội (đã mã hóa)';
COMMENT ON COLUMN social_accounts.refresh_token IS 'Refresh token từ mạng xã hội (đã mã hóa)';
COMMENT ON COLUMN social_accounts.token_expires_at IS 'Thời gian hết hạn của access token';
COMMENT ON COLUMN social_accounts.profile_data IS 'Dữ liệu thô về hồ sơ người dùng từ mạng xã hội';
COMMENT ON COLUMN social_accounts.created_at IS 'Thời gian tạo bản ghi';
COMMENT ON COLUMN social_accounts.updated_at IS 'Thời gian cập nhật bản ghi gần nhất';
COMMENT ON COLUMN social_accounts.tenant_id IS 'ID của công ty/tổ chức sở hữu bản ghi';
