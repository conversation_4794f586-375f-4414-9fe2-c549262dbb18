import { IsNotEmpty, IsString } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho yêu cầu gửi lại mã OTP
 */
export class ResendOtpDto {
  @ApiProperty({
    description: 'Token xác thực OTP',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsNotEmpty({ message: 'Token không được để trống' })
  @IsString({ message: 'Token phải là chuỗi' })
  token: string;
}
