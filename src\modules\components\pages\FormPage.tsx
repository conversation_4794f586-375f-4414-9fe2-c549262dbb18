import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { Card, Icon } from '@/shared/components/common';

interface FormComponentCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  to: string;
}

const FormComponentCard: React.FC<FormComponentCardProps> = ({ title, description, icon, to }) => {
  return (
    <Link to={to} className="block">
      <Card className="h-full transition-all duration-300 hover:shadow-md hover:-translate-y-1">
        <div className="flex items-start">
          <div className="mr-4 p-3 bg-primary/10 text-primary rounded-lg">{icon}</div>
          <div>
            <h3 className="text-lg font-medium mb-2">{title}</h3>
            <p className="text-gray-600 dark:text-gray-400">{description}</p>
          </div>
        </div>
      </Card>
    </Link>
  );
};

const FormPage: React.FC = () => {
  const { t } = useTranslation();

  const formComponents = [
    {
      title: t('components.form.basic.title', 'Basic Form Components'),
      description: t(
        'components.form.basic.description',
        'Form, FormItem, Input, and other basic form components'
      ),
      icon: <Icon name="layout" size="lg" />,
      to: '/components/form-components',
    },
    {
      title: t('components.form.conditional.title', 'Conditional Form Fields'),
      description: t(
        'components.form.conditional.description',
        'Show or hide fields based on conditions'
      ),
      icon: <Icon name="filter" size="lg" />,
      to: '/components/form-conditional-fields',
    },
    {
      title: t('components.formSections.title', 'Form Sections'),
      description: t(
        'components.formSections.description',
        'Organize forms into collapsible sections'
      ),
      icon: <Icon name="layers" size="lg" />,
      to: '/components/form-sections',
    },
    {
      title: t('components.form.grid.title', 'Form Layouts'),
      description: t(
        'components.form.grid.description',
        'Grid, inline, and horizontal form layouts'
      ),
      icon: <Icon name="grid" size="lg" />,
      to: '/components/form-layouts',
    },
    {
      title: t('components.form.array.title', 'Form Arrays'),
      description: t(
        'components.form.array.description',
        'Dynamic form fields with add/remove functionality'
      ),
      icon: <Icon name="layers" size="lg" />,
      to: '/components/form-array',
    },
    {
      title: t('components.form.apiForm.title', 'API Form Integration'),
      description: t('components.form.apiForm.description', 'Integrate forms with API calls'),
      icon: <Icon name="server" size="lg" />,
      to: '/components/form-api-integration',
    },
    {
      title: t('components.form.dependencies.title', 'Form Field Dependencies'),
      description: t(
        'components.form.dependencies.description',
        'Create dependent fields like cascading dropdowns'
      ),
      icon: <Icon name="link" size="lg" />,
      to: '/components/form-dependencies',
    },
  ];

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.form.title', 'Form Components')}
        </h1>
        <p className="text-muted">
          {t(
            'components.form.description',
            'A collection of form components for building forms with various layouts and behaviors.'
          )}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {formComponents.map((component, index) => (
          <FormComponentCard
            key={index}
            title={component.title}
            description={component.description}
            icon={component.icon}
            to={component.to}
          />
        ))}
      </div>

      <div className="mt-12">
        <h2 className="text-xl font-bold mb-4">
          {t('components.form.examples.title', 'Form Examples')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {t(
            'components.form.examples.description',
            'Complete form examples for common use cases.'
          )}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Link to="/form-demo" className="block">
            <Card className="h-full transition-all duration-300 hover:shadow-md hover:-translate-y-1">
              <h3 className="text-lg font-medium mb-2">Login & Registration Forms</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Complete examples of login and registration forms with validation.
              </p>
              <div className="flex justify-end">
                <span className="text-primary flex items-center">
                  View Example <Icon name="chevron-right" size="sm" className="ml-1" />
                </span>
              </div>
            </Card>
          </Link>

          <Link to="/form-conditional-demo" className="block">
            <Card className="h-full transition-all duration-300 hover:shadow-md hover:-translate-y-1">
              <h3 className="text-lg font-medium mb-2">Multi-step Form</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Example of a multi-step form with progress tracking and validation.
              </p>
              <div className="flex justify-end">
                <span className="text-primary flex items-center">
                  View Example <Icon name="chevron-right" size="sm" className="ml-1" />
                </span>
              </div>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FormPage;
