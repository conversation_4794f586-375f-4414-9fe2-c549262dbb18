import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import useSlideForm from '@/shared/hooks/useSlideForm';

import AffiliateRankForm from '../components/forms/AffiliateRankForm';
import { useAffiliateRankData } from '../hooks/useAffiliateData';
import {
  AffiliateRankDto,
  AffiliateRankStatus,
  AffiliateRankQueryDto,
} from '../types/affiliate.types';

/**
 * Trang quản lý cấp bậc affiliate
 */
const AffiliateRankListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Lấy hook từ useAffiliateRankData
  const { useRanks } = useAffiliateRankData();

  // State cho filter và hiển thị
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // State cho phân trang và sắp xếp
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection | null>(null);

  // Không cần định nghĩa columnLabelMap nữa vì sẽ sử dụng title từ columns

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    const params: AffiliateRankQueryDto = {
      page: currentPage,
      limit: pageSize,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection || undefined,
    };

    // Thêm filter theo trạng thái nếu không phải 'all'
    if (filter !== 'all') {
      params.status = filter as AffiliateRankStatus;
    }

    return params;
  }, [currentPage, pageSize, searchTerm, sortBy, sortDirection, filter]);

  // Gọi API lấy danh sách rank
  const { data: rankData, isLoading } = useRanks(queryParams);

  // Columns cho bảng
  const columns = useMemo<TableColumn<AffiliateRankDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'name',
        title: t('affiliate:rank.table.name'),
        dataIndex: 'name',
        width: '15%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('affiliate:rank.table.description'),
        dataIndex: 'description',
        width: '25%',
        sortable: true,
      },
      {
        key: 'minReferrals',
        title: t('affiliate:rank.table.minReferrals'),
        dataIndex: 'minReferrals',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}</div>;
        },
      },
      {
        key: 'commissionRate',
        title: t('affiliate:rank.table.commissionRate'),
        dataIndex: 'commissionRate',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}%</div>;
        },
      },
      {
        key: 'status',
        title: t('affiliate:rank.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as AffiliateRankStatus;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === AffiliateRankStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {status === AffiliateRankStatus.ACTIVE ? t('common:active') : t('common:inactive')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: AffiliateRankDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => console.log('View', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:edit')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => console.log('Edit', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => console.log('Delete', record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t]
  );

  // Tự động tạo visibleColumns từ columns
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>(() => {
    // Tạo mảng visibleColumns từ columns
    const columnVisibility: ColumnVisibility[] = [
      // Thêm option "Tất cả"
      { id: 'all', label: 'Tất cả', visible: true },
      // Thêm các cột từ columns
      ...columns.map(column => ({
        id: column.key,
        label: typeof column.title === 'string' ? column.title : column.key,
        visible: true,
      })),
    ];
    return columnVisibility;
  });

  // Cập nhật visibleColumns khi columns thay đổi
  useEffect(() => {
    // Sử dụng functional update để tránh dependency vào visibleColumns
    setVisibleColumns(prevColumns => {
      // Lưu trữ trạng thái visible hiện tại
      const currentVisibility: Record<string, boolean> = {};
      for (const col of prevColumns) {
        currentVisibility[col.id] = col.visible;
      }

      // Kiểm tra xem có cột mới hoặc cột bị xóa không
      const currentColumnIds = prevColumns.filter(col => col.id !== 'all').map(col => col.id);
      const newColumnIds = columns.map(col => col.key);

      // Chỉ cập nhật nếu có sự thay đổi về cấu trúc cột
      const hasStructuralChanges =
        newColumnIds.length !== currentColumnIds.length ||
        newColumnIds.some(id => !currentColumnIds.includes(id)) ||
        currentColumnIds.some(id => !newColumnIds.includes(id));

      if (hasStructuralChanges) {
        // Tạo mảng visibleColumns mới từ columns, giữ nguyên trạng thái visible
        return [
          // Thêm option "Tất cả", giữ nguyên trạng thái
          {
            id: 'all',
            label: 'Tất cả',
            visible: currentVisibility['all'] !== undefined ? currentVisibility['all'] : true,
          },
          // Thêm các cột từ columns, giữ nguyên trạng thái nếu đã tồn tại
          ...columns.map(column => ({
            id: column.key,
            label: typeof column.title === 'string' ? column.title : column.key,
            visible:
              currentVisibility[column.key] !== undefined ? currentVisibility[column.key] : true,
          })),
        ];
      }

      // Nếu không có thay đổi, giữ nguyên state cũ
      return prevColumns;
    });
  }, [columns]);

  // Xử lý dữ liệu từ API
  const tableData = useMemo(() => {
    if (!rankData) {
      return {
        items: [],
        totalItems: 0,
        currentPage: 1,
        totalPages: 0,
      };
    }

    // Dữ liệu đã được lọc và phân trang từ API
    return {
      items: rankData.items,
      totalItems: rankData.meta.totalItems,
      currentPage: rankData.meta.currentPage,
      totalPages: rankData.meta.totalPages,
    };
  }, [rankData]);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    // Reset về trang 1 khi tìm kiếm
    setCurrentPage(1);
  }, []);

  // Xử lý thay đổi filter
  const handleFilterChange = useCallback((filterValue: string) => {
    setFilter(filterValue);
    // Reset về trang 1 khi thay đổi filter
    setCurrentPage(1);
  }, []);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm rank
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
    console.log('Lọc theo khoảng thời gian:', range);
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    // Chuyển đổi từ SortOrder (Table) sang SortDirection (API)
    let direction: SortDirection | null = null;
    if (order === 'asc') {
      direction = SortDirection.ASC;
    } else if (order === 'desc') {
      direction = SortDirection.DESC;
    }

    setSortBy(column);
    setSortDirection(direction);
    console.log(`Sắp xếp theo: ${column}, hướng: ${direction}`);
  }, []);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== pageSize) {
        setPageSize(newPageSize);
      }
      console.log(`Chuyển đến trang ${page}, kích thước trang: ${newPageSize}`);
    },
    [pageSize]
  );

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = (columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
    console.log('Thay đổi hiển thị cột:', columns);
  };

  // Tạo danh sách các filter item
  const filterItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('common:all'),
      icon: 'list',
      onClick: () => handleFilterChange('all'),
    },
    {
      id: 'active',
      label: t('common:active'),
      icon: 'check',
      onClick: () => handleFilterChange(AffiliateRankStatus.ACTIVE),
    },
    {
      id: 'inactive',
      label: t('common:inactive'),
      icon: 'eye-off',
      onClick: () => handleFilterChange(AffiliateRankStatus.INACTIVE),
    },
  ];

  // Lọc các cột hiển thị dựa trên visibleColumns
  const visibleTableColumns = useMemo(() => {
    return columns.filter(col => {
      const columnSetting = visibleColumns.find(vc => vc.id === col.key);
      return columnSetting?.visible !== false;
    });
  }, [columns, visibleColumns]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAdd}
        items={filterItems}
        onDateRangeChange={handleDateRangeChange}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        columns={visibleColumns}
        tableColumns={columns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      <SlideInForm isVisible={isVisible}>
        <AffiliateRankForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={visibleTableColumns}
          data={tableData.items}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={handleSortChange}
          pagination={{
            current: tableData.currentPage,
            pageSize: pageSize,
            total: tableData.totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AffiliateRankListPage;
