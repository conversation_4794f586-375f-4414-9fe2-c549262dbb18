import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaFacebook } from 'react-icons/fa';
import { SiGoogle, <PERSON><PERSON>alo } from 'react-icons/si';

import logoImage from '@/shared/assets/images/logo/logo-erp.png';
import { Button, Card, ResponsiveImage } from '@/shared/components/common';

import LoginForm from '../components/LoginForm';
import RegisterForm from '../components/RegisterForm';

/**
 * Login page component
 */
const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('login');
  const [, setShowForgotPassword] = useState<boolean>(false);

  // <PERSON>le forgot password button click
  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  // Handle Google login
  const handleGoogleLogin = () => {};

  // Handle Facebook login
  const handleFacebookLogin = () => {};

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-3">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-16">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-full object-contain max-w-[50%]"
          />
        </div>
      </div>

      <div className="mb-6">
        <div className="grid grid-cols-2 gap-0 border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            className={`py-3 font-medium text-sm transition-colors ${
              activeTab === 'login'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('login')}
          >
            {t('auth.login')}
          </button>
          <button
            className={`py-3 font-medium text-sm transition-colors ${
              activeTab === 'register'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('register')}
          >
            {t('auth.register')}
          </button>
        </div>

        {activeTab === 'login' && (
          <div className="space-y-6">
            <LoginForm onForgotPassword={handleForgotPassword} />

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-dark text-gray-500 dark:text-gray-400">
                  {t('auth:orContinueWith')}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-3">
              <Button
                variant="ghost"
                className="py-2 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-blue-400 dark:hover:border-blue-500"
                onClick={handleFacebookLogin}
                aria-label={t('auth.loginWithFacebook')}
              >
                <FaFacebook className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </Button>
              <Button
                variant="ghost"
                className="py-2 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-red-400 dark:hover:border-red-500"
                onClick={handleGoogleLogin}
                aria-label={t('auth.loginWithGoogle')}
              >
                <SiGoogle className="h-4 w-4 text-red-500 dark:text-red-400" />
              </Button>
              <Button
                variant="ghost"
                className="py-2 flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-blue-400 dark:hover:border-blue-500"
                aria-label={t('auth.loginWithZalo')}
              >
                <SiZalo className="h-5 w-5 text-blue-500 dark:text-blue-400" />
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'register' && (
          <div className="space-y-6">
            <RegisterForm />
          </div>
        )}
      </div>
    </Card>
  );
};

export default LoginPage;
