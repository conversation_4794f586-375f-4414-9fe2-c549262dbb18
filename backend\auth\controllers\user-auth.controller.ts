import { AppException } from '@/common/exceptions/app.exception';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';

import { CurrentUser } from '../decorators/current-user.decorator';
import { ChangePasswordDto, ChangePasswordResponseDto } from '../dto/change-password.dto';
import { RegisterEmployeeDto, RegisterEmployeeResponseDto } from '../dto/register-employee.dto';
import { ForgotPasswordDto, ForgotPasswordResponseDto, ResetPasswordDto, ResetPasswordResponseDto } from '../dto/reset-password.dto';
import { UserLoginDto } from '../dto/user-login.dto';
import { UserLoginResponseDto, UserResponseDto } from '../dto/user-response.dto';
import { AUTH_ERROR_CODE } from '../errors/auth-error.code';
import { JwtUserGuard } from '../guards/jwt-user.guard';
import { JwtPayload } from '../guards/jwt.util';
import { UserAuthService } from '../services/user-auth.service';

@ApiTags(SWAGGER_API_TAG.AUTHENTICATION)
@ApiExtraModels(
  ApiResponseDto,
  UserLoginResponseDto,
  UserResponseDto,
  ChangePasswordResponseDto,
  ForgotPasswordResponseDto,
  ResetPasswordResponseDto,
  RegisterEmployeeResponseDto
)
@Controller('auth/user')
export class UserAuthController {
  constructor(private readonly userAuthService: UserAuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'Đăng nhập tài khoản người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Đăng nhập thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({ status: 401, description: 'Thông tin đăng nhập không chính xác' })
  async login(@Body() loginDto: UserLoginDto): Promise<ApiResponseDto<UserLoginResponseDto>> {
    return this.userAuthService.login(loginDto);
  }

  @Get('profile')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Lấy thông tin người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin thành công',
    schema: ApiResponseDto.getSchema(UserLoginResponseDto),
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getProfile(@CurrentUser() user: any): Promise<ApiResponseDto<UserLoginResponseDto>> {
    return this.userAuthService.getUserProfile(user.id);
  }

  @Post('change-password')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Đổi mật khẩu người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Đổi mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ChangePasswordResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc mật khẩu hiện tại không chính xác' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async changePassword(
    @CurrentUser() user: JwtPayload,
    @Body() changePasswordDto: ChangePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    return this.userAuthService.changePassword(user.id, changePasswordDto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Yêu cầu đặt lại mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Gửi email đặt lại mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ForgotPasswordResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<ApiResponseDto<ForgotPasswordResponseDto>> {
    return this.userAuthService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Đặt lại mật khẩu' })
  @ApiResponse({
    status: 200,
    description: 'Đặt lại mật khẩu thành công',
    schema: ApiResponseDto.getSchema(ResetPasswordResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc token không hợp lệ' })
  async resetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
  ): Promise<ApiResponseDto<ResetPasswordResponseDto>> {
    return this.userAuthService.resetPassword(resetPasswordDto);
  }

  @Post('register-employee')
  @UseGuards(JwtUserGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Đăng ký tài khoản nhân viên mới' })
  @ApiResponse({
    status: 201,
    description: 'Đăng ký tài khoản nhân viên thành công',
    schema: ApiResponseDto.getSchema(RegisterEmployeeResponseDto),
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ hoặc tài khoản đã tồn tại' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async registerEmployee(
    @CurrentUser() user: JwtPayload,
    @Body() registerDto: RegisterEmployeeDto,
  ): Promise<ApiResponseDto<RegisterEmployeeResponseDto>> {
    // Kiểm tra xem người dùng có thuộc về một công ty/tổ chức không
    if (!user.tenantId) {
      throw new AppException(
        AUTH_ERROR_CODE.UNAUTHORIZED_ACCESS,
        'Không có thông tin công ty/tổ chức',
      );
    }
    return this.userAuthService.registerEmployee(registerDto);
  }
}
