import { IsEmail, IsNotEmpty, IsOptional, IsString, Length, Matches } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin người dùng quản trị khi đăng ký công ty
 */
export class AdminUserDto {
  @ApiProperty({
    description: 'Tên đăng nhập của người quản trị',
    example: 'admin',
  })
  @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  @Length(3, 50, { message: 'Tên đăng nhập phải từ 3 đến 50 ký tự' })
  username: string;

  @ApiProperty({
    description: 'Họ tên đầy đủ của người quản trị',
    example: '<PERSON><PERSON><PERSON>n <PERSON>ăn <PERSON>',
  })
  @IsNotEmpty({ message: 'Họ tên không được để trống' })
  @IsString({ message: 'Họ tên phải là chuỗi' })
  @Length(3, 100, { message: 'Họ tên phải từ 3 đến 100 ký tự' })
  fullName: string;
}

/**
 * DTO cho đăng ký tài khoản công ty
 */
export class CompanyRegisterDto {
  @ApiProperty({
    description: 'Tên công ty',
    example: 'Công ty TNHH ABC',
  })
  @IsNotEmpty({ message: 'Tên công ty không được để trống' })
  @IsString({ message: 'Tên công ty phải là chuỗi' })
  @Length(3, 255, { message: 'Tên công ty phải từ 3 đến 255 ký tự' })
  companyName: string;

  @ApiProperty({
    description: 'Email liên hệ của công ty',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  companyEmail: string;

  @ApiProperty({
    description: 'Mật khẩu tài khoản',
    example: 'StrongPassword123!',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @Length(8, 50, { message: 'Mật khẩu phải từ 8 đến 50 ký tự' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%&*?@])[\d!$%&*?@A-Za-z]+$/, {
    message:
      'Mật khẩu phải chứa ít nhất một chữ cái viết thường, một chữ cái viết hoa, một chữ số và một ký tự đặc biệt',
  })
  password: string;

  @ApiProperty({
    description: 'Số điện thoại liên hệ của công ty',
    example: '0912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @Length(10, 15, { message: 'Số điện thoại phải từ 10 đến 15 ký tự' })
  phoneNumber?: string;

  @ApiProperty({
    description: 'Token reCAPTCHA từ client',
    example: '03AGdBq24PBCbwiDRgMN...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  recaptchaToken?: string;

  @ApiProperty({
    description: 'Thông tin người quản trị',
    type: AdminUserDto,
  })
  @IsNotEmpty({ message: 'Thông tin người quản trị không được để trống' })
  adminUser: AdminUserDto;
}
