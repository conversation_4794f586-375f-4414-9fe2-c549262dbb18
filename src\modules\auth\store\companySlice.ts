import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { Company } from '../types/company-auth.types';

/**
 * Interface cho thông tin đăng ký công ty
 */
export interface RegisterInfo {
  otpToken: string;
  expiresAt: number;
  maskedEmail?: string;
  otp?: string;
  message?: string;
}

/**
 * Trạng thái công ty
 */
export interface CompanyState {
  company: Company | null;
  isLoading: boolean;
  error: string | null;
  registerInfo: RegisterInfo | null;
}

/**
 * Trạng thái công ty ban đầu
 */
const initialState: CompanyState = {
  company: null,
  isLoading: false,
  error: null,
  registerInfo: null,
};

/**
 * Company slice
 */
const companySlice = createSlice({
  name: 'company',
  initialState,
  reducers: {
    /**
     * Cập nhật thông tin công ty
     */
    setCompany: (state, action: PayloadAction<Company>) => {
      state.company = action.payload;
      state.error = null;
    },

    /**
     * Đặt trạng thái loading
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    /**
     * Đặt thông báo lỗi
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },

    /**
     * Xóa thông tin công ty
     */
    clearCompany: state => {
      state.company = null;
      state.error = null;
    },

    /**
     * Lưu thông tin đăng ký
     */
    setRegisterInfo: (state, action: PayloadAction<RegisterInfo>) => {
      state.registerInfo = action.payload;
    },

    /**
     * Xóa thông tin đăng ký
     */
    clearRegisterInfo: state => {
      state.registerInfo = null;
    },
  },
});

// Export actions
export const {
  setCompany,
  setLoading,
  setError,
  clearCompany,
  setRegisterInfo,
  clearRegisterInfo,
} = companySlice.actions;

// Export reducer
export default companySlice.reducer;
