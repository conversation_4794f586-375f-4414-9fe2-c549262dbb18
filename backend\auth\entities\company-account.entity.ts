import { Entity, PrimaryColumn, Column } from 'typeorm';

import { CompanyStatus } from '../enum/company-status.enum';

/**
 * Entity representing company accounts in the system
 */
@Entity('company_accounts')
export class CompanyAccount {
  /**
   * Unique identifier for the company account
   */
  @PrimaryColumn({ type: 'bigint' })
  id: number;

  /**
   * Company name
   */
  @Column({ name: 'company_name', type: 'varchar', length: 255, nullable: true })
  companyName: string | null;

  /**
   * Subdomain used to access the system
   */
  @Column({ type: 'varchar', length: 255, nullable: true, unique: true })
  subdomain: string | null;

  /**
   * Company tax code
   */
  @Column({ name: 'tax_code', type: 'varchar', length: 100, nullable: true })
  taxCode: string | null;

  /**
   * Company contact email
   */
  @Column({ name: 'company_email', type: 'varchar', length: 255, nullable: true })
  companyEmail: string | null;


  /**
   * Company contact phone number
   */
  @Column({ name: 'phone_number', type: 'varchar', length: 50, nullable: true })
  phoneNumber: string | null;

  /**
   * Company address
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  address: string | null;

  /**
   * Account status (ACTIVE, INACTIVE, SUSPENDED)
   */
  @Column({ type: 'enum', enum: CompanyStatus, nullable: true })
  status: CompanyStatus | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;
}
