import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { Button, IconButton, Icon } from '@/shared/components/common';

import { ComponentDemo } from '../components';

/**
 * Demo component for loading buttons
 */
const LoadingButtonDemo: React.FC = () => {
  const { t } = useTranslation();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({
    primary: false,
    secondary: false,
    outline: false,
    success: false,
    warning: false,
    danger: false,
    small: false,
    medium: false,
    large: false,
    fullWidth: false,
    withIcon: false,
    iconOnly: false,
    iconPrimary: false,
    iconOutline: false,
    iconSmall: false,
    iconLarge: false,
  });

  // Toggle loading state for a specific button
  const toggleLoading = (key: string) => {
    setLoadingStates(prev => {
      const newState = { ...prev, [key]: !prev[key] };

      // Auto reset after 3 seconds
      if (newState[key]) {
        setTimeout(() => {
          setLoadingStates(current => ({ ...current, [key]: false }));
        }, 3000);
      }

      return newState;
    });
  };

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.buttons.loading.title', 'Loading Buttons')}
        </h1>
        <p className="text-muted">
          {t(
            'components.buttons.loading.description',
            'Buttons with loading states for better user experience during async operations.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.buttons.loading.variants', 'Loading Button Variants')}
        description={t(
          'components.buttons.loading.variantsDescription',
          'Different button variants with loading states.'
        )}
        code={`import { Button } from '@/shared/components/common';

// Primary loading button
<Button
  variant="primary"
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Primary Button'}
</Button>

// Secondary loading button
<Button
  variant="secondary"
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Secondary Button'}
</Button>

// Other variants...`}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('common.primary', 'Primary')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="primary"
                isLoading={loadingStates.primary}
                onClick={() => toggleLoading('primary')}
              >
                {loadingStates.primary
                  ? t('common.loading', 'Loading...')
                  : t('common.primary', 'Primary Button')}
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.secondary', 'Secondary')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="secondary"
                isLoading={loadingStates.secondary}
                onClick={() => toggleLoading('secondary')}
              >
                {loadingStates.secondary
                  ? t('common.loading', 'Loading...')
                  : t('common.secondary', 'Secondary Button')}
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.outline', 'Outline')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="outline"
                isLoading={loadingStates.outline}
                onClick={() => toggleLoading('outline')}
              >
                {loadingStates.outline
                  ? t('common.loading', 'Loading...')
                  : t('common.outline', 'Outline Button')}
              </Button>
            </div>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.loading.sizes', 'Loading Button Sizes')}
        description={t(
          'components.buttons.loading.sizesDescription',
          'Different button sizes with loading states.'
        )}
        code={`import { Button } from '@/shared/components/common';

// Small loading button
<Button
  variant="primary"
  size="sm"
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Small Button'}
</Button>

// Medium loading button
<Button
  variant="primary"
  size="md"
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Medium Button'}
</Button>

// Large loading button
<Button
  variant="primary"
  size="lg"
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Large Button'}
</Button>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('common.small', 'Small')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="primary"
                size="sm"
                isLoading={loadingStates.small}
                onClick={() => toggleLoading('small')}
              >
                {loadingStates.small
                  ? t('common.loading', 'Loading...')
                  : t('common.small', 'Small Button')}
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.medium', 'Medium (Default)')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="primary"
                size="md"
                isLoading={loadingStates.medium}
                onClick={() => toggleLoading('medium')}
              >
                {loadingStates.medium
                  ? t('common.loading', 'Loading...')
                  : t('common.medium', 'Medium Button')}
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.large', 'Large')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="primary"
                size="lg"
                isLoading={loadingStates.large}
                onClick={() => toggleLoading('large')}
              >
                {loadingStates.large
                  ? t('common.loading', 'Loading...')
                  : t('common.large', 'Large Button')}
              </Button>
            </div>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.loading.special', 'Special Loading Buttons')}
        description={t(
          'components.buttons.loading.specialDescription',
          'Special button configurations with loading states.'
        )}
        code={`import { Button, Icon } from '@/shared/components/common';

// Full width loading button
<Button
  variant="primary"
  fullWidth
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Loading...' : 'Full Width Button'}
</Button>

// Loading button with icon
<Button
  variant="primary"
  leftIcon={<Icon name="save" size="sm" />}
  isLoading={isLoading}
  onClick={toggleLoading}
>
  {isLoading ? 'Saving...' : 'Save Changes'}
</Button>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">
              {t('components.buttons.fullWidth.title', 'Full Width')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded">
              <Button
                variant="primary"
                fullWidth
                isLoading={loadingStates.fullWidth}
                onClick={() => toggleLoading('fullWidth')}
              >
                {loadingStates.fullWidth
                  ? t('common.loading', 'Loading...')
                  : t('components.buttons.fullWidth.button', 'Full Width Button')}
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              {t('components.buttons.withIcons.title', 'With Icon')}
            </h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <Button
                variant="primary"
                leftIcon={<Icon name="save" size="sm" />}
                isLoading={loadingStates.withIcon}
                onClick={() => toggleLoading('withIcon')}
              >
                {loadingStates.withIcon
                  ? t('common.saving', 'Saving...')
                  : t('common.saveChanges', 'Save Changes')}
              </Button>
            </div>
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.buttons.loading.iconButtons', 'Icon Buttons with Loading State')}
        description={t(
          'components.buttons.loading.iconButtonsDescription',
          'Icon buttons with loading states.'
        )}
        code={`import { IconButton } from '@/shared/components/common';

// Default icon button with loading state
<IconButton
  icon="save"
  isLoading={isLoading}
  onClick={toggleLoading}
  title="Save"
/>

// Primary icon button with loading state
<IconButton
  icon="save"
  variant="primary"
  isLoading={isLoading}
  onClick={toggleLoading}
  title="Save"
/>

// Other variants...`}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="font-semibold mb-2">{t('common.default', 'Default')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <IconButton
                icon="save"
                isLoading={loadingStates.iconOnly}
                onClick={() => toggleLoading('iconOnly')}
                title={t('common.save', 'Save')}
              />
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.primary', 'Primary')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <IconButton
                icon="save"
                variant="primary"
                isLoading={loadingStates.iconPrimary}
                onClick={() => toggleLoading('iconPrimary')}
                title={t('common.save', 'Save')}
              />
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">{t('common.outline', 'Outline')}</h3>
            <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
              <IconButton
                icon="save"
                variant="outline"
                isLoading={loadingStates.iconOutline}
                onClick={() => toggleLoading('iconOutline')}
                title={t('common.save', 'Save')}
              />
            </div>
          </div>
        </div>
      </ComponentDemo>

      <div className="mt-8 flex justify-center">
        <Link to="/components/buttons">
          <Button variant="outline" leftIcon={<Icon name="chevron-left" size="sm" />}>
            {t('common.backToButtons', 'Back to Buttons')}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default LoadingButtonDemo;
