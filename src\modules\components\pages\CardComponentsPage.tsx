import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  ResourceCard,
  ProductCard,
  BlogCard,
  UserCard,
  StatisticCard,
  EventCard,
  NotificationCard,
  PricingCard,
  FeatureCard,
  TestimonialCard,
  ProjectCard,
  TeamMemberCard,
  TaskCard,
  CourseCard,
  AnalyticsCard,
  TransactionCard,
} from '@/shared/components/cards';
import { Container, Typography } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

/**
 * Trang hiển thị các card components
 */
const CardComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  // Dữ liệu mẫu cho ResourceCard
  const resourceData = {
    id: '1',
    name: '<PERSON>à<PERSON> liệu hướng dẫn sử dụng.pdf',
    type: 'document' as const,
    fileSize: 2500000,
    url: 'https://example.com/document.pdf',
    createdAt: new Date().toISOString(),
    extension: 'pdf',
  };

  // Dữ liệu mẫu cho ProductCard
  const productData = {
    id: '1',
    name: 'Laptop Dell XPS 13',
    price: ********,
    originalPrice: 30000000,
    discount: 17,
    image:
      'https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?q=80&w=1000&auto=format&fit=crop',
    rating: 4.5,
    ratingCount: 120,
    status: 'in-stock' as const,
  };

  // Dữ liệu mẫu cho ProductCard thứ hai
  const productData2 = {
    id: '2',
    name: 'Smartphone Samsung Galaxy S21',
    price: 18000000,
    originalPrice: 20000000,
    discount: 10,
    image:
      'https://images.unsplash.com/photo-1610945415295-d9bbf067e59c?q=80&w=1000&auto=format&fit=crop',
    rating: 4.2,
    ratingCount: 85,
    status: 'in-stock' as const,
  };

  // Dữ liệu mẫu cho BlogCard
  const blogData = {
    id: '1',
    title: 'Hướng dẫn sử dụng React Hooks trong dự án',
    excerpt:
      'React Hooks là một tính năng mới được giới thiệu trong React 16.8, cho phép bạn sử dụng state và các tính năng khác của React mà không cần viết class...',
    thumbnail:
      'https://images.unsplash.com/photo-1633356122102-3fe601e05bd2?q=80&w=1000&auto=format&fit=crop',
    author: {
      name: 'Nguyễn Văn A',
      avatar:
        'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
    },
    publishedAt: new Date().toISOString(),
    readingTime: 5,
    tags: [
      { id: '1', name: 'React', slug: 'react' },
      { id: '2', name: 'JavaScript', slug: 'javascript' },
    ],
    commentCount: 8,
    viewCount: 256,
  };

  // Dữ liệu mẫu cho BlogCard thứ hai
  const blogData2 = {
    id: '2',
    title: 'Tối ưu hiệu suất ứng dụng React với useMemo và useCallback',
    excerpt:
      'Trong bài viết này, chúng ta sẽ tìm hiểu cách sử dụng useMemo và useCallback để tối ưu hiệu suất ứng dụng React của bạn...',
    thumbnail:
      'https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=1000&auto=format&fit=crop',
    author: {
      name: 'Trần Thị B',
      avatar:
        'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
    },
    publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    readingTime: 7,
    tags: [
      { id: '1', name: 'React', slug: 'react' },
      { id: '3', name: 'Performance', slug: 'performance' },
    ],
    commentCount: 12,
    viewCount: 320,
  };

  // Dữ liệu mẫu cho UserCard
  const userData = {
    id: '1',
    name: 'Nguyễn Văn A',
    avatar:
      'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
    title: 'Frontend Developer',
    department: 'Engineering',
    email: '<EMAIL>',
    phone: '**********',
    status: 'online' as const,
    skills: ['React', 'TypeScript', 'Tailwind CSS'],
  };

  // Dữ liệu mẫu cho UserCard thứ hai
  const userData2 = {
    id: '2',
    name: 'Trần Thị B',
    avatar:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
    title: 'UX Designer',
    department: 'Design',
    email: '<EMAIL>',
    phone: '0987654321',
    status: 'offline' as const,
    skills: ['Figma', 'Adobe XD', 'UI/UX', 'Prototyping'],
  };

  // Dữ liệu mẫu cho StatisticCard
  const statisticData = {
    title: 'Doanh thu',
    value: '1.250.000 đ',
    change: 12.5,
    trend: 'up' as const,
    icon: 'arrow-up' as const,
    iconColor: 'success' as const,
    chartData: [10, 25, 15, 30, 20, 35, 45],
    chartType: 'line' as const,
  };

  // Dữ liệu mẫu cho StatisticCard thứ hai
  const statisticData2 = {
    title: 'Đơn hàng',
    value: '156',
    change: -5.2,
    trend: 'down' as const,
    icon: 'arrow-down' as const,
    iconColor: 'danger' as const,
    chartData: [45, 40, 35, 30, 25, 20, 15],
    chartType: 'bar' as const,
  };

  // Dữ liệu mẫu cho EventCard
  const eventData = {
    id: '1',
    title: 'Hội thảo công nghệ',
    description: 'Hội thảo về các công nghệ mới trong lĩnh vực phát triển web và mobile',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
    location: 'Hà Nội',
    isOnline: true,
    meetingUrl: 'https://meet.example.com',
    organizer: {
      name: 'Công ty ABC',
      avatar:
        'https://images.unsplash.com/photo-**********-f129b911e442?q=80&w=100&auto=format&fit=crop',
    },
    attendees: [
      {
        id: '1',
        name: 'Nguyễn Văn A',
        avatar:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
      },
      {
        id: '2',
        name: 'Trần Thị B',
        avatar:
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
      },
      {
        id: '3',
        name: 'Lê Văn C',
        avatar:
          'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?q=80&w=100&auto=format&fit=crop',
      },
    ],
    status: 'upcoming' as const,
    thumbnail:
      'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=1000&auto=format&fit=crop',
  };

  // Dữ liệu mẫu cho NotificationCard
  const notificationData = {
    id: '1',
    title: 'Thông báo mới',
    content: 'Bạn có một thông báo mới từ hệ thống. Vui lòng kiểm tra và xác nhận.',
    createdAt: new Date().toISOString(),
    type: 'info' as const,
    isRead: false,
    sender: {
      id: '1',
      name: 'Hệ thống',
      avatar:
        'https://images.unsplash.com/photo-**********-f129b911e442?q=80&w=100&auto=format&fit=crop',
    },
  };

  // Dữ liệu mẫu cho NotificationCard thứ hai
  const notificationData2 = {
    id: '2',
    title: 'Cảnh báo bảo mật',
    content: 'Phát hiện đăng nhập bất thường vào tài khoản của bạn. Vui lòng kiểm tra và xác nhận.',
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    type: 'warning' as const,
    isRead: true,
    sender: {
      id: '2',
      name: 'Bảo mật',
      avatar:
        'https://images.unsplash.com/photo-1633332755192-727a05c4013d?q=80&w=100&auto=format&fit=crop',
    },
  };

  // Dữ liệu mẫu cho PricingCard
  const pricingData = {
    name: 'Gói Pro',
    price: '299.000đ',
    billingCycle: 'tháng',
    description: 'Gói dịch vụ phù hợp cho doanh nghiệp vừa và nhỏ',
    features: [
      'Không giới hạn người dùng',
      'Hỗ trợ 24/7',
      'Tích hợp API',
      'Báo cáo nâng cao',
      'Sao lưu dữ liệu hàng ngày',
    ],
    excludedFeatures: ['Tùy chỉnh giao diện', 'Quản lý đa chi nhánh'],
    icon: 'star' as const,
    color: 'primary' as const,
    isPopular: true,
  };

  // Dữ liệu mẫu cho PricingCard thứ hai
  const pricingData2 = {
    name: 'Gói Enterprise',
    price: '999.000đ',
    billingCycle: 'tháng',
    description: 'Giải pháp toàn diện cho doanh nghiệp lớn',
    features: [
      'Tất cả tính năng của gói Pro',
      'Tùy chỉnh giao diện',
      'Quản lý đa chi nhánh',
      'API không giới hạn',
      'Hỗ trợ triển khai',
      'Đào tạo nhân viên',
    ],
    icon: 'star' as const,
    color: 'success' as const,
  };

  // Dữ liệu mẫu cho FeatureCard
  const featureData = {
    title: 'Tích hợp AI',
    description: 'Tự động hóa quy trình làm việc với trí tuệ nhân tạo tiên tiến',
    icon: 'code' as const,
    iconColor: 'primary',
  };

  // Dữ liệu mẫu cho FeatureCard thứ hai
  const featureData2 = {
    title: 'Bảo mật tối đa',
    description: 'Bảo vệ dữ liệu của bạn với các công nghệ mã hóa tiên tiến',
    icon: 'lock' as const,
    iconColor: 'success',
  };

  // Dữ liệu mẫu cho TestimonialCard
  const testimonialData = {
    content:
      'Sản phẩm tuyệt vời! Đã giúp công ty chúng tôi tăng hiệu suất làm việc lên 30% chỉ trong vòng 2 tháng.',
    author: 'Nguyễn Văn A',
    role: 'CEO, Công ty XYZ',
    avatar:
      'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
    rating: 5,
    date: '15/05/2023',
  };

  // Dữ liệu mẫu cho TestimonialCard thứ hai
  const testimonialData2 = {
    content:
      'Giao diện dễ sử dụng, tính năng đầy đủ. Đội ngũ hỗ trợ rất nhiệt tình và chuyên nghiệp.',
    author: 'Trần Thị B',
    role: 'Marketing Manager, Công ty ABC',
    avatar:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
    rating: 4.5,
    date: '20/06/2023',
  };

  // Dữ liệu mẫu cho ProjectCard
  const projectData = {
    id: '1',
    name: 'Xây dựng website thương mại điện tử',
    description:
      'Phát triển website bán hàng trực tuyến với đầy đủ tính năng quản lý sản phẩm, đơn hàng và thanh toán',
    thumbnail:
      'https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1000&auto=format&fit=crop',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'in-progress' as const,
    progress: 65,
    client: 'Công ty ABC',
    budget: '200.000.000đ',
    members: [
      {
        id: '1',
        name: 'Nguyễn Văn A',
        avatar:
          'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
      },
      {
        id: '2',
        name: 'Trần Thị B',
        avatar:
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
      },
      {
        id: '3',
        name: 'Lê Văn C',
        avatar:
          'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?q=80&w=100&auto=format&fit=crop',
      },
    ],
    tags: [
      { id: '1', name: 'E-commerce', color: 'blue' },
      { id: '2', name: 'Web Development', color: 'green' },
    ],
  };

  // Dữ liệu mẫu cho TeamMemberCard
  const teamMemberData = {
    id: '1',
    name: 'Nguyễn Văn A',
    position: 'Senior Developer',
    department: 'Engineering',
    avatar:
      'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
    bio: 'Chuyên gia phát triển web với hơn 8 năm kinh nghiệm trong các dự án lớn',
    email: '<EMAIL>',
    phone: '**********',
    location: 'Hà Nội, Việt Nam',
    skills: ['React', 'Node.js', 'TypeScript', 'MongoDB'],
    socialLinks: [
      { type: 'linkedin', url: 'https://linkedin.com' },
      { type: 'github', url: 'https://github.com' },
    ],
  };

  // Dữ liệu mẫu cho TeamMemberCard thứ hai
  const teamMemberData2 = {
    id: '2',
    name: 'Trần Thị B',
    position: 'UX Designer',
    department: 'Design',
    avatar:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
    bio: 'Nhà thiết kế UX/UI với kinh nghiệm trong các dự án fintech và e-commerce',
    email: '<EMAIL>',
    phone: '0987654321',
    location: 'TP. Hồ Chí Minh, Việt Nam',
    skills: ['Figma', 'Adobe XD', 'UI/UX', 'Prototyping'],
    socialLinks: [
      { type: 'linkedin', url: 'https://linkedin.com' },
      { type: 'instagram', url: 'https://instagram.com' },
    ],
  };

  // Dữ liệu mẫu cho TaskCard
  const taskData = {
    id: '1',
    title: 'Thiết kế giao diện người dùng',
    description: 'Thiết kế giao diện người dùng cho ứng dụng di động theo yêu cầu của khách hàng',
    priority: 'high' as const,
    status: 'in-progress' as const,
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    estimatedTime: 240,
    assignee: {
      id: '1',
      name: 'Nguyễn Văn A',
      avatar:
        'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
    },
    watchers: [
      {
        id: '2',
        name: 'Trần Thị B',
        avatar:
          'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
      },
      {
        id: '3',
        name: 'Lê Văn C',
        avatar:
          'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?q=80&w=100&auto=format&fit=crop',
      },
    ],
    subtasks: [
      { id: '1-1', title: 'Phân tích yêu cầu', completed: true },
      { id: '1-2', title: 'Thiết kế wireframe', completed: true },
      { id: '1-3', title: 'Thiết kế UI', completed: false },
      { id: '1-4', title: 'Tạo prototype', completed: false },
    ],
    attachmentsCount: 3,
    commentsCount: 5,
    labels: [
      { id: '1', name: 'UI/UX', color: 'blue' },
      { id: '2', name: 'Mobile', color: 'green' },
    ],
  };

  // Dữ liệu mẫu cho CourseCard
  const courseData = {
    id: '1',
    title: 'Lập trình React.js từ cơ bản đến nâng cao',
    description:
      'Khóa học toàn diện về React.js, bao gồm các khái niệm cơ bản và kỹ thuật nâng cao',
    thumbnail:
      'https://images.unsplash.com/photo-1633356122102-3fe601e05bd2?q=80&w=1000&auto=format&fit=crop',
    instructor: {
      id: '1',
      name: 'Nguyễn Văn A',
      avatar:
        'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
      title: 'Senior Frontend Developer',
      rating: 4.8,
    },
    lessonsCount: 42,
    duration: 1800,
    level: 'intermediate' as const,
    rating: 4.7,
    ratingCount: 320,
    progress: 35,
    tags: [
      { id: '1', name: 'React' },
      { id: '2', name: 'JavaScript' },
      { id: '3', name: 'Frontend' },
    ],
    price: 1200000,
    originalPrice: 1500000,
    isEnrolled: true,
  };

  // Dữ liệu mẫu cho CourseCard thứ hai
  const courseData2 = {
    id: '2',
    title: 'Thiết kế UI/UX chuyên nghiệp với Figma',
    description:
      'Học cách thiết kế giao diện người dùng đẹp mắt và trải nghiệm người dùng tuyệt vời với Figma',
    thumbnail:
      'https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=1000&auto=format&fit=crop',
    instructor: {
      id: '2',
      name: 'Trần Thị B',
      avatar:
        'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=100&auto=format&fit=crop',
      title: 'UX Designer',
      rating: 4.9,
    },
    lessonsCount: 36,
    duration: 1500,
    level: 'all-levels' as const,
    rating: 4.8,
    ratingCount: 215,
    tags: [
      { id: '4', name: 'UI/UX' },
      { id: '5', name: 'Figma' },
      { id: '6', name: 'Design' },
    ],
    price: 990000,
    originalPrice: 1290000,
    isBestseller: true,
    isEnrolled: false,
  };

  // Dữ liệu mẫu cho AnalyticsCard
  const analyticsData = {
    labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
    datasets: [
      {
        label: 'Doanh thu',
        data: [1200, 1900, 1500, 2200, 1800, 2500, 3000],
        color: '#3B82F6',
      },
      {
        label: 'Chi phí',
        data: [800, 1200, 900, 1500, 1300, 1700, 2000],
        color: '#EF4444',
      },
    ],
  };

  // Dữ liệu mẫu cho TransactionCard
  const transactionData = {
    id: '1',
    title: 'Thanh toán hóa đơn điện',
    description: 'Thanh toán hóa đơn điện tháng 5/2023',
    amount: 850000,
    type: 'expense' as const,
    date: new Date().toISOString(),
    category: {
      id: '1',
      name: 'Hóa đơn & Tiện ích',
      icon: 'home' as const,
      color: 'blue',
    },
    paymentMethod: {
      id: '1',
      name: 'Thẻ tín dụng',
      icon: 'credit-card' as const,
      lastDigits: '4567',
    },
    recipient: {
      id: '1',
      name: 'Công ty Điện lực',
      accountNumber: '**********',
    },
    status: 'completed' as const,
    hasReceipt: true,
    note: 'Thanh toán đúng hạn',
    tags: ['Hóa đơn', 'Điện'],
  };

  // Dữ liệu mẫu cho TransactionCard thứ hai
  const transactionData2 = {
    id: '2',
    title: 'Nhận lương tháng 5',
    description: 'Lương tháng 5/2023',
    amount: ********,
    type: 'income' as const,
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    category: {
      id: '2',
      name: 'Lương',
      icon: 'dollar-sign' as const,
      color: 'green',
    },
    paymentMethod: {
      id: '2',
      name: 'Chuyển khoản ngân hàng',
      icon: 'link' as const,
    },
    recipient: {
      id: '2',
      name: 'Công ty ABC',
      avatar:
        'https://images.unsplash.com/photo-**********-f129b911e442?q=80&w=100&auto=format&fit=crop',
    },
    status: 'completed' as const,
    tags: ['Lương', 'Thu nhập'],
  };

  // Xử lý các actions
  const handleAction = (action: string, data: unknown) => {
    console.log(`${action} action:`, data);
  };

  return (
    <Container>
      <Typography variant="h2" className="mb-6">
        {t('components.cards.title', 'Card Components')}
      </Typography>

      <Typography variant="body1" className="mb-8">
        {t(
          'components.cards.description',
          'Các components hiển thị thông tin dưới dạng card cho nhiều loại nội dung khác nhau.'
        )}
      </Typography>

      {/* ResourceCard */}
      <ComponentDemo
        title={t('components.cards.resource.title', 'Resource Card')}
        description={t(
          'components.cards.resource.description',
          'Card hiển thị thông tin về tệp tài nguyên như hình ảnh, tài liệu, video.'
        )}
        code={`import { ResourceCard } from '@/shared/components/cards';

<ResourceCard
  resource={{
    id: '1',
    name: 'Tài liệu hướng dẫn sử dụng.pdf',
    type: 'document',
    fileSize: 2500000,
    url: 'https://example.com/document.pdf',
    createdAt: new Date().toISOString(),
    extension: 'pdf'
  }}
  onDownload={handleDownload}
  onPreview={handlePreview}
  onDelete={handleDelete}
/>`}
      >
        <div className="w-full max-w-xs">
          <ResourceCard
            resource={resourceData}
            onDownload={resource => handleAction('download', resource)}
            onPreview={resource => handleAction('preview', resource)}
            onDelete={resource => handleAction('delete', resource)}
          />
        </div>
      </ComponentDemo>

      {/* ProductCard */}
      <ComponentDemo
        title={t('components.cards.product.title', 'Product Card')}
        description={t(
          'components.cards.product.description',
          'Card hiển thị thông tin về sản phẩm với hình ảnh, giá, đánh giá.'
        )}
        code={`import { ProductCard } from '@/shared/components/cards';

<ProductCard
  product={{
    id: '1',
    name: 'Laptop Dell XPS 13',
    price: ********,
    originalPrice: 30000000,
    discount: 17,
    image: 'https://example.com/product.jpg',
    rating: 4.5,
    ratingCount: 120,
    status: 'in-stock'
  }}
  onAddToCart={handleAddToCart}
  onToggleFavorite={handleToggleFavorite}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <ProductCard
            product={productData}
            onAddToCart={product => handleAction('addToCart', product)}
            onToggleFavorite={(product, isFavorite) =>
              handleAction('toggleFavorite', { product, isFavorite })
            }
          />
          <ProductCard
            product={productData2}
            onAddToCart={product => handleAction('addToCart', product)}
            onToggleFavorite={(product, isFavorite) =>
              handleAction('toggleFavorite', { product, isFavorite })
            }
          />
        </div>
      </ComponentDemo>

      {/* BlogCard */}
      <ComponentDemo
        title={t('components.cards.blog.title', 'Blog Card')}
        description={t(
          'components.cards.blog.description',
          'Card hiển thị thông tin về bài viết blog với thumbnail, tiêu đề, tag và thông tin tác giả.'
        )}
        code={`import { BlogCard } from '@/shared/components/cards';

<BlogCard
  blog={{
    id: '1',
    title: 'Hướng dẫn sử dụng React Hooks trong dự án',
    excerpt: 'React Hooks là một tính năng mới...',
    thumbnail: 'https://example.com/thumbnail.jpg',
    author: {
      name: 'Nguyễn Văn A',
      avatar: 'https://example.com/avatar.jpg'
    },
    publishedAt: new Date().toISOString(),
    readingTime: 5,
    tags: [
      { id: '1', name: 'React', slug: 'react' },
      { id: '2', name: 'JavaScript', slug: 'javascript' }
    ],
    commentCount: 8,
    viewCount: 256
  }}
  onTagClick={handleTagClick}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <BlogCard
            blog={blogData}
            onTagClick={(tagSlug, e) => handleAction('tagClick', { tagSlug, e })}
          />
          <BlogCard
            blog={blogData2}
            onTagClick={(tagSlug, e) => handleAction('tagClick', { tagSlug, e })}
          />
        </div>
      </ComponentDemo>

      {/* UserCard */}
      <ComponentDemo
        title={t('components.cards.user.title', 'User Card')}
        description={t(
          'components.cards.user.description',
          'Card hiển thị thông tin về người dùng với avatar, tên, chức danh và các thông tin khác.'
        )}
        code={`import { UserCard } from '@/shared/components/cards';

<UserCard
  user={{
    id: '1',
    name: 'Nguyễn Văn A',
    avatar: 'https://example.com/avatar.jpg',
    title: 'Frontend Developer',
    department: 'Engineering',
    email: '<EMAIL>',
    phone: '**********',
    status: 'online',
    skills: ['React', 'TypeScript', 'Tailwind CSS']
  }}
  onMessage={handleMessage}
  onViewProfile={handleViewProfile}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <UserCard
            user={userData}
            onMessage={user => handleAction('message', user)}
            onViewProfile={user => handleAction('viewProfile', user)}
          />
          <UserCard
            user={userData2}
            onMessage={user => handleAction('message', user)}
            onViewProfile={user => handleAction('viewProfile', user)}
          />
        </div>
      </ComponentDemo>

      {/* StatisticCard */}
      <ComponentDemo
        title={t('components.cards.statistic.title', 'Statistic Card')}
        description={t(
          'components.cards.statistic.description',
          'Card hiển thị thông tin thống kê với icon, số liệu và biểu đồ mini.'
        )}
        code={`import { StatisticCard } from '@/shared/components/cards';

<StatisticCard
  title="Doanh thu"
  value="1.250.000 đ"
  change={12.5}
  trend="up"
  icon="trending-up"
  iconColor="success"
  chartData={[10, 25, 15, 30, 20, 35, 45]}
  chartType="line"
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <StatisticCard
            title={statisticData.title}
            value={statisticData.value}
            change={statisticData.change}
            trend={statisticData.trend}
            icon={statisticData.icon}
            iconColor={statisticData.iconColor}
            chartData={statisticData.chartData}
            chartType={statisticData.chartType}
          />
          <StatisticCard
            title={statisticData2.title}
            value={statisticData2.value}
            change={statisticData2.change}
            trend={statisticData2.trend}
            icon={statisticData2.icon}
            iconColor={statisticData2.iconColor}
            chartData={statisticData2.chartData}
            chartType={statisticData2.chartType}
          />
        </div>
      </ComponentDemo>

      {/* EventCard */}
      <ComponentDemo
        title={t('components.cards.event.title', 'Event Card')}
        description={t(
          'components.cards.event.description',
          'Card hiển thị thông tin về sự kiện với ngày, giờ, địa điểm và người tham gia.'
        )}
        code={`import { EventCard } from '@/shared/components/cards';

<EventCard
  event={{
    id: '1',
    title: 'Hội thảo công nghệ',
    description: 'Hội thảo về các công nghệ mới...',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
    location: 'Hà Nội',
    isOnline: true,
    meetingUrl: 'https://meet.example.com',
    status: 'upcoming'
  }}
  onJoin={handleJoin}
  onAddToCalendar={handleAddToCalendar}
/>`}
      >
        <div className="w-full max-w-xs">
          <EventCard
            event={eventData}
            onJoin={event => handleAction('join', event)}
            onAddToCalendar={event => handleAction('addToCalendar', event)}
          />
        </div>
      </ComponentDemo>

      {/* NotificationCard */}
      <ComponentDemo
        title={t('components.cards.notification.title', 'Notification Card')}
        description={t(
          'components.cards.notification.description',
          'Card hiển thị thông báo với icon, tiêu đề, nội dung và thời gian.'
        )}
        code={`import { NotificationCard } from '@/shared/components/cards';

<NotificationCard
  notification={{
    id: '1',
    title: 'Thông báo mới',
    content: 'Bạn có một thông báo mới từ hệ thống...',
    createdAt: new Date().toISOString(),
    type: 'info',
    isRead: false,
    sender: {
      id: '1',
      name: 'Hệ thống',
      avatar: 'https://example.com/avatar.jpg'
    }
  }}
  onMarkAsRead={handleMarkAsRead}
  onDelete={handleDelete}
/>`}
      >
        <div className="flex flex-col gap-4 w-full max-w-md">
          <NotificationCard
            notification={notificationData}
            onMarkAsRead={notification => handleAction('markAsRead', notification)}
            onDelete={notification => handleAction('delete', notification)}
          />
          <NotificationCard
            notification={notificationData2}
            onMarkAsRead={notification => handleAction('markAsRead', notification)}
            onDelete={notification => handleAction('delete', notification)}
          />
        </div>
      </ComponentDemo>

      {/* PricingCard */}
      <ComponentDemo
        title={t('components.cards.pricing.title', 'Pricing Card')}
        description={t(
          'components.cards.pricing.description',
          'Card hiển thị thông tin gói dịch vụ/sản phẩm với giá, tính năng và nút đăng ký.'
        )}
        code={`import { PricingCard } from '@/shared/components/cards';

<PricingCard
  name="Gói Pro"
  price="299.000đ"
  billingCycle="tháng"
  description="Gói dịch vụ phù hợp cho doanh nghiệp vừa và nhỏ"
  features={[
    'Không giới hạn người dùng',
    'Hỗ trợ 24/7',
    'Tích hợp API',
    'Báo cáo nâng cao',
    'Sao lưu dữ liệu hàng ngày'
  ]}
  excludedFeatures={[
    'Tùy chỉnh giao diện',
    'Quản lý đa chi nhánh'
  ]}
  icon="star"
  color="primary"
  isPopular={true}
  onSubscribe={handleSubscribe}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <PricingCard
            {...pricingData}
            onSubscribe={() => handleAction('subscribe', pricingData)}
          />
          <PricingCard
            {...pricingData2}
            onSubscribe={() => handleAction('subscribe', pricingData2)}
          />
        </div>
      </ComponentDemo>

      {/* FeatureCard */}
      <ComponentDemo
        title={t('components.cards.feature.title', 'Feature Card')}
        description={t(
          'components.cards.feature.description',
          'Card hiển thị tính năng nổi bật với icon, tiêu đề và mô tả.'
        )}
        code={`import { FeatureCard } from '@/shared/components/cards';

<FeatureCard
  title="Tích hợp AI"
  description="Tự động hóa quy trình làm việc với trí tuệ nhân tạo tiên tiến"
  icon="code"
  iconColor="primary"
  onClick={handleFeatureClick}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <FeatureCard {...featureData} onClick={() => handleAction('featureClick', featureData)} />
          <FeatureCard
            {...featureData2}
            onClick={() => handleAction('featureClick', featureData2)}
          />
        </div>
      </ComponentDemo>

      {/* TestimonialCard */}
      <ComponentDemo
        title={t('components.cards.testimonial.title', 'Testimonial Card')}
        description={t(
          'components.cards.testimonial.description',
          'Card hiển thị đánh giá/nhận xét của khách hàng với avatar, tên và nội dung.'
        )}
        code={`import { TestimonialCard } from '@/shared/components/cards';

<TestimonialCard
  content="Sản phẩm tuyệt vời! Đã giúp công ty chúng tôi tăng hiệu suất làm việc lên 30% chỉ trong vòng 2 tháng."
  author="Nguyễn Văn A"
  role="CEO, Công ty XYZ"
  avatar="https://example.com/avatar.jpg"
  rating={5}
  date="15/05/2023"
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <TestimonialCard {...testimonialData} />
          <TestimonialCard {...testimonialData2} />
        </div>
      </ComponentDemo>

      {/* ProjectCard */}
      <ComponentDemo
        title={t('components.cards.project.title', 'Project Card')}
        description={t(
          'components.cards.project.description',
          'Card hiển thị thông tin dự án với tiến độ, thành viên và thẻ.'
        )}
        code={`import { ProjectCard } from '@/shared/components/cards';

<ProjectCard
  project={{
    id: '1',
    name: 'Xây dựng website thương mại điện tử',
    description: 'Phát triển website bán hàng trực tuyến...',
    thumbnail: 'https://example.com/thumbnail.jpg',
    startDate: new Date().toISOString(),
    endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'in-progress',
    progress: 65,
    client: 'Công ty ABC',
    budget: '200.000.000đ',
    members: [
      { id: '1', name: 'Nguyễn Văn A', avatar: 'https://example.com/avatar1.jpg' },
      { id: '2', name: 'Trần Thị B', avatar: 'https://example.com/avatar2.jpg' }
    ],
    tags: [
      { id: '1', name: 'E-commerce', color: 'blue' },
      { id: '2', name: 'Web Development', color: 'green' }
    ]
  }}
  onViewDetails={handleViewDetails}
  onTagClick={handleTagClick}
/>`}
      >
        <div className="w-full max-w-md">
          <ProjectCard
            project={projectData}
            onViewDetails={project => handleAction('viewProjectDetails', project)}
            onTagClick={tagId => handleAction('projectTagClick', tagId)}
          />
        </div>
      </ComponentDemo>

      {/* TeamMemberCard */}
      <ComponentDemo
        title={t('components.cards.teamMember.title', 'Team Member Card')}
        description={t(
          'components.cards.teamMember.description',
          'Card hiển thị thông tin thành viên nhóm với avatar, tên, chức danh và kỹ năng.'
        )}
        code={`import { TeamMemberCard } from '@/shared/components/cards';

<TeamMemberCard
  member={{
    id: '1',
    name: 'Nguyễn Văn A',
    position: 'Senior Developer',
    department: 'Engineering',
    avatar: 'https://example.com/avatar.jpg',
    bio: 'Chuyên gia phát triển web với hơn 8 năm kinh nghiệm...',
    email: '<EMAIL>',
    phone: '**********',
    location: 'Hà Nội, Việt Nam',
    skills: ['React', 'Node.js', 'TypeScript', 'MongoDB'],
    socialLinks: [
      { type: 'linkedin', url: 'https://linkedin.com' },
      { type: 'github', url: 'https://github.com' }
    ]
  }}
  onContact={handleContact}
  onViewProfile={handleViewProfile}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <TeamMemberCard
            member={teamMemberData}
            onContact={member => handleAction('contactMember', member)}
            onViewProfile={member => handleAction('viewMemberProfile', member)}
          />
          <TeamMemberCard
            member={teamMemberData2}
            layout="horizontal"
            onContact={member => handleAction('contactMember', member)}
            onViewProfile={member => handleAction('viewMemberProfile', member)}
          />
        </div>
      </ComponentDemo>

      {/* TaskCard */}
      <ComponentDemo
        title={t('components.cards.task.title', 'Task Card')}
        description={t(
          'components.cards.task.description',
          'Card hiển thị và quản lý công việc với mức độ ưu tiên, trạng thái và tiến độ.'
        )}
        code={`import { TaskCard } from '@/shared/components/cards';

<TaskCard
  task={{
    id: '1',
    title: 'Thiết kế giao diện người dùng',
    description: 'Thiết kế giao diện người dùng cho ứng dụng di động...',
    priority: 'high',
    status: 'in-progress',
    dueDate: new Date().toISOString(),
    estimatedTime: 240,
    assignee: {
      id: '1',
      name: 'Nguyễn Văn A',
      avatar: 'https://example.com/avatar.jpg'
    },
    subtasks: [
      { id: '1-1', title: 'Phân tích yêu cầu', completed: true },
      { id: '1-2', title: 'Thiết kế wireframe', completed: true },
      { id: '1-3', title: 'Thiết kế UI', completed: false },
      { id: '1-4', title: 'Tạo prototype', completed: false }
    ],
    labels: [
      { id: '1', name: 'UI/UX', color: 'blue' },
      { id: '2', name: 'Mobile', color: 'green' }
    ]
  }}
  onEdit={handleEdit}
  onStatusChange={handleStatusChange}
  onViewDetails={handleViewDetails}
/>`}
      >
        <div className="w-full max-w-lg">
          <TaskCard
            task={taskData}
            onEdit={task => handleAction('editTask', task)}
            onStatusChange={(task, newStatus) =>
              handleAction('changeTaskStatus', { task, newStatus })
            }
            onViewDetails={task => handleAction('viewTaskDetails', task)}
          />
        </div>
      </ComponentDemo>

      {/* CourseCard */}
      <ComponentDemo
        title={t('components.cards.course.title', 'Course Card')}
        description={t(
          'components.cards.course.description',
          'Card hiển thị thông tin khóa học với hình ảnh, giảng viên, đánh giá và tiến độ học tập.'
        )}
        code={`import { CourseCard } from '@/shared/components/cards';

<CourseCard
  course={{
    id: '1',
    title: 'Lập trình React.js từ cơ bản đến nâng cao',
    description: 'Khóa học toàn diện về React.js...',
    thumbnail: 'https://example.com/thumbnail.jpg',
    instructor: {
      id: '1',
      name: 'Nguyễn Văn A',
      avatar: 'https://example.com/avatar.jpg',
      title: 'Senior Frontend Developer'
    },
    lessonsCount: 42,
    duration: 1800,
    level: 'intermediate',
    rating: 4.7,
    ratingCount: 320,
    progress: 35,
    price: 1200000,
    originalPrice: 1500000,
    isEnrolled: true
  }}
  onContinue={handleContinue}
  onViewDetails={handleViewDetails}
/>`}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl">
          <CourseCard
            course={courseData}
            onContinue={course => handleAction('continueCourse', course)}
            onViewDetails={course => handleAction('viewCourseDetails', course)}
          />
          <CourseCard
            course={courseData2}
            onEnroll={course => handleAction('enrollCourse', course)}
            onViewDetails={course => handleAction('viewCourseDetails', course)}
          />
        </div>
      </ComponentDemo>

      {/* AnalyticsCard */}
      <ComponentDemo
        title={t('components.cards.analytics.title', 'Analytics Card')}
        description={t(
          'components.cards.analytics.description',
          'Card hiển thị dữ liệu phân tích với biểu đồ, giá trị hiện tại và xu hướng.'
        )}
        code={`import { AnalyticsCard } from '@/shared/components/cards';

<AnalyticsCard
  title="Doanh thu và chi phí"
  description="Biểu đồ doanh thu và chi phí theo tháng"
  chartType="line"
  data={{
    labels: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
    datasets: [
      {
        label: 'Doanh thu',
        data: [1200, 1900, 1500, 2200, 1800, 2500, 3000],
        color: '#3B82F6'
      },
      {
        label: 'Chi phí',
        data: [800, 1200, 900, 1500, 1300, 1700, 2000],
        color: '#EF4444'
      }
    ]
  }}
  currentValue="3.000.000 đ"
  previousValue="2.500.000 đ"
  changePercentage={20}
  trend="up"
  icon="trending-up"
  iconColor="primary"
  timeRangeOptions={[
    { value: 'day', label: 'Ngày' },
    { value: 'week', label: 'Tuần' },
    { value: 'month', label: 'Tháng' },
    { value: 'year', label: 'Năm' }
  ]}
  onTimeRangeChange={handleTimeRangeChange}
  onExport={handleExport}
/>`}
      >
        <div className="w-full max-w-2xl">
          <AnalyticsCard
            title="Doanh thu và chi phí"
            description="Biểu đồ doanh thu và chi phí theo tháng"
            chartType="line"
            data={analyticsData}
            currentValue="3.000.000 đ"
            previousValue="2.500.000 đ"
            changePercentage={20}
            trend="up"
            icon="trending-up"
            iconColor="primary"
            timeRangeOptions={[
              { value: 'day', label: 'Ngày' },
              { value: 'week', label: 'Tuần' },
              { value: 'month', label: 'Tháng' },
              { value: 'year', label: 'Năm' },
            ]}
            onTimeRangeChange={timeRange => handleAction('changeTimeRange', timeRange)}
            onExport={() => handleAction('exportAnalytics', {})}
            chartHeight={200}
          />
        </div>
      </ComponentDemo>

      {/* TransactionCard */}
      <ComponentDemo
        title={t('components.cards.transaction.title', 'Transaction Card')}
        description={t(
          'components.cards.transaction.description',
          'Card hiển thị giao dịch tài chính với loại giao dịch, số tiền, danh mục và trạng thái.'
        )}
        code={`import { TransactionCard } from '@/shared/components/cards';

<TransactionCard
  transaction={{
    id: '1',
    title: 'Thanh toán hóa đơn điện',
    description: 'Thanh toán hóa đơn điện tháng 5/2023',
    amount: 850000,
    type: 'expense',
    date: new Date().toISOString(),
    category: {
      id: '1',
      name: 'Hóa đơn & Tiện ích',
      icon: 'home',
      color: 'blue'
    },
    paymentMethod: {
      id: '1',
      name: 'Thẻ tín dụng',
      icon: 'credit-card',
      lastDigits: '4567'
    },
    status: 'completed',
    hasReceipt: true
  }}
  onViewDetails={handleViewDetails}
  onViewReceipt={handleViewReceipt}
  currency="VND"
/>`}
      >
        <div className="flex flex-col gap-4 w-full max-w-lg">
          <TransactionCard
            transaction={transactionData}
            onViewDetails={transaction => handleAction('viewTransactionDetails', transaction)}
            onViewReceipt={transaction => handleAction('viewTransactionReceipt', transaction)}
          />
          <TransactionCard
            transaction={transactionData2}
            onViewDetails={transaction => handleAction('viewTransactionDetails', transaction)}
          />
        </div>
      </ComponentDemo>
    </Container>
  );
};

export default CardComponentsPage;
