import { format } from 'date-fns';
import React from 'react';

import { Icon, Typography } from '@/shared/components/common';
import { cn } from '@/shared/utils/cn';

import { CalendarEvent } from '../../types';

import EventBadge from './EventBadge';

export interface EventCardProps {
  /**
   * Sự kiện cần hiển thị
   */
  event: CalendarEvent;

  /**
   * Hiển thị chi tiết đầy đủ hay chỉ hiển thị tóm tắt
   */
  detailed?: boolean;

  /**
   * CSS class bổ sung
   */
  className?: string;

  /**
   * Callback khi click vào card
   */
  onClick?: () => void;
}

/**
 * Component hiển thị thông tin sự kiện dưới dạng card
 *
 * @example
 * ```tsx
 * <EventCard event={event} />
 * <EventCard event={event} detailed onClick={handleEventClick} />
 * ```
 */
const EventCard: React.FC<EventCardProps> = ({ event, detailed = false, className, onClick }) => {
  // Xác định loại sự kiện
  const eventType = event.extendedProps?.type || 'default';

  // Format thời gian
  const formatTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'HH:mm');
  };

  // Format ngày
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'dd/MM/yyyy');
  };

  // Xác định CSS class dựa trên loại sự kiện
  const cardClass = cn(
    'calendar-event-card rounded-lg p-3 shadow-sm transition-all hover:shadow-md',
    `calendar-event-${eventType}`,
    {
      'cursor-pointer hover:translate-y-[-2px]': !!onClick,
    },
    className
  );

  return (
    <div className={cardClass} onClick={onClick}>
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <EventBadge type={eventType as string} showLabel={detailed} />
          <Typography variant="h6" className="font-semibold">
            {event.title}
          </Typography>
        </div>
        {event.allDay && (
          <span className="text-xs bg-primary-muted text-primary px-1.5 py-0.5 rounded">
            Cả ngày
          </span>
        )}
      </div>

      <div className="flex items-center gap-1 text-sm text-muted mb-1">
        <Icon name="calendar" size="xs" />
        <span>
          {formatDate(event.start)}
          {!event.allDay && ` ${formatTime(event.start)}`}
          {event.end && (
            <>
              {' - '}
              {formatDate(event.start) !== formatDate(event.end) && formatDate(event.end)}
              {!event.allDay && ` ${formatTime(event.end)}`}
            </>
          )}
        </span>
      </div>

      {detailed && event.location && (
        <div className="flex items-center gap-1 text-sm text-muted mb-1">
          <Icon name="map-pin" size="xs" />
          <span>{event.location}</span>
        </div>
      )}

      {detailed && event.description && (
        <div className="mt-2 text-sm">
          <p>{event.description}</p>
        </div>
      )}
    </div>
  );
};

export default EventCard;
