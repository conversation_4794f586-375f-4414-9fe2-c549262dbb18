import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button, Tabs } from '@/shared/components/common';

import FileUploader from './FileUploader';
import { AttachmentFile, ACCEPTED_FILE_TYPES } from './types';

/**
 * Component demo cho FileUploader
 */
const FileUploaderDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho danh sách tệp
  const [files, setFiles] = useState<AttachmentFile[]>([]);
  const [activeTab, setActiveTab] = useState<string>('all');

  // <PERSON><PERSON><PERSON> lập tải lên tệp
  const mockUpload = async (file: File): Promise<string> => {
    // <PERSON><PERSON><PERSON> lập delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Tr<PERSON> về URL gi<PERSON> lập
    return URL.createObjectURL(file);
  };

  // Reset lựa chọn
  const handleReset = () => {
    setFiles([]);
  };

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('calendar.fileUploaderDemo', 'Demo FileUploader')}
      </Typography>

      <Tabs
        items={[
          {
            key: 'all',
            label: t('calendar.allFiles', 'Tất cả tệp'),
            children: (
              <Card className="p-4 mt-4">
                <Typography variant="h3" className="mb-4">
                  {t('calendar.uploadFiles', 'Tải lên tệp')}
                </Typography>

                <FileUploader
                  files={files}
                  onChange={setFiles}
                  label={t('calendar.attachments', 'Tệp đính kèm')}
                  maxSize={5}
                  maxFiles={5}
                  accept={ACCEPTED_FILE_TYPES.ALL}
                  dragDrop={true}
                  onUpload={mockUpload}
                />

                {files.length > 0 && (
                  <div className="mt-6 p-3 bg-card-muted rounded-md">
                    <Typography variant="h4" className="mb-2">
                      {t('calendar.uploadedFiles', 'Tệp đã tải lên')} ({files.length}):
                    </Typography>
                    <pre className="text-sm overflow-auto">{JSON.stringify(files, null, 2)}</pre>
                  </div>
                )}

                <div className="mt-6 flex justify-center">
                  <Button variant="outline" onClick={handleReset} className="px-4 py-2">
                    {t('common.reset', 'Đặt lại')}
                  </Button>
                </div>
              </Card>
            ),
          },
          {
            key: 'images',
            label: t('calendar.images', 'Hình ảnh'),
            children: (
              <Card className="p-4 mt-4">
                <Typography variant="h3" className="mb-4">
                  {t('calendar.uploadImages', 'Tải lên hình ảnh')}
                </Typography>

                <FileUploader
                  files={files}
                  onChange={setFiles}
                  label={t('calendar.imageAttachments', 'Hình ảnh đính kèm')}
                  maxSize={2}
                  maxFiles={5}
                  accept={ACCEPTED_FILE_TYPES.IMAGE}
                  dragDrop={true}
                  onUpload={mockUpload}
                />

                {files.length > 0 && (
                  <div className="mt-6 p-3 bg-card-muted rounded-md">
                    <Typography variant="h4" className="mb-2">
                      {t('calendar.uploadedImages', 'Hình ảnh đã tải lên')} ({files.length}):
                    </Typography>
                    <pre className="text-sm overflow-auto">{JSON.stringify(files, null, 2)}</pre>
                  </div>
                )}

                <div className="mt-6 flex justify-center">
                  <Button variant="outline" onClick={handleReset} className="px-4 py-2">
                    {t('common.reset', 'Đặt lại')}
                  </Button>
                </div>
              </Card>
            ),
          },
          {
            key: 'documents',
            label: t('calendar.documents', 'Tài liệu'),
            children: (
              <Card className="p-4 mt-4">
                <Typography variant="h3" className="mb-4">
                  {t('calendar.uploadDocuments', 'Tải lên tài liệu')}
                </Typography>

                <FileUploader
                  files={files}
                  onChange={setFiles}
                  label={t('calendar.documentAttachments', 'Tài liệu đính kèm')}
                  maxSize={10}
                  maxFiles={5}
                  accept={ACCEPTED_FILE_TYPES.DOCUMENT}
                  dragDrop={true}
                  onUpload={mockUpload}
                />

                {files.length > 0 && (
                  <div className="mt-6 p-3 bg-card-muted rounded-md">
                    <Typography variant="h4" className="mb-2">
                      {t('calendar.uploadedDocuments', 'Tài liệu đã tải lên')} ({files.length}):
                    </Typography>
                    <pre className="text-sm overflow-auto">{JSON.stringify(files, null, 2)}</pre>
                  </div>
                )}

                <div className="mt-6 flex justify-center">
                  <Button variant="outline" onClick={handleReset} className="px-4 py-2">
                    {t('common.reset', 'Đặt lại')}
                  </Button>
                </div>
              </Card>
            ),
          },
        ]}
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      />
    </div>
  );
};

export default FileUploaderDemo;
