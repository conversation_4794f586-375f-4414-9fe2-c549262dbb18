import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

import {
  AffiliateManagementPage,
  // AffiliateRankListPage,
  AffiliateOrderListPage,
  PublisherListPage,
} from '@/modules/admin/affiliate';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

import AffiliateRankListPageOptimized from '../pages/AffiliateRankListPageOptimized';
import AffiliateRankListPageWithHooks from '../pages/AffiliateRankListPageWithHooks';

/**
 * Routes cho module affiliate
 */

export const affiliateRoutes: RouteObject[] = [
  {
    path: '/admin/affiliate',
    element: (
      <MainLayout title="Quản lý Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin/affiliate/publishers',
    element: (
      <MainLayout title="Quản lý Publisher">
        <Suspense fallback={<Loading />}>
          <PublisherListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin/affiliate/ranks',
    element: (
      <MainLayout title="Quản lý Cấp bậc Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateRankListPageOptimized />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin/affiliate/ranks-with-hooks',
    element: (
      <MainLayout title="Quản lý Cấp bậc Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateRankListPageWithHooks />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/admin/affiliate/orders',
    element: (
      <MainLayout title="Quản lý Đơn hàng Affiliate">
        <Suspense fallback={<Loading />}>
          <AffiliateOrderListPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default affiliateRoutes;
