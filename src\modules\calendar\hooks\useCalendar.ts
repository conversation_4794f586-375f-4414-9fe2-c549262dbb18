import { addHours, startOfDay } from 'date-fns';
import { useState, useRef, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';
import FullCalendar from '@fullcalendar/react';

import { CalendarEvent } from '../types';

export interface UseCalendarOptions {
  /**
   * Sự kiện ban đầu
   */
  initialEvents?: CalendarEvent[];

  /**
   * Chế độ xem ban đầu
   */
  initialView?: 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay' | 'listWeek';

  /**
   * Callback khi thêm sự kiện
   */
  onAddEvent?: (event: CalendarEvent) => void;

  /**
   * Callback khi cập nhật sự kiện
   */
  onUpdateEvent?: (event: CalendarEvent) => void;

  /**
   * Callback khi xóa sự kiện
   */
  onDeleteEvent?: (eventId: string) => void;
}

/**
 * Hook quản lý state và logic cho calendar
 *
 * @example
 * ```tsx
 * const {
 *   events,
 *   currentView,
 *   calendarRef,
 *   calendarTitle,
 *   handleDateSelect,
 *   handleEventClick,
 *   handleEventChange,
 *   handleViewChange,
 *   handlePrev,
 *   handleNext,
 *   handleToday,
 *   addEvent,
 *   updateEvent,
 *   deleteEvent,
 * } = useCalendar({
 *   initialEvents: myEvents,
 *   initialView: 'dayGridMonth',
 *   onAddEvent: (event) => saveEventToAPI(event),
 * });
 * ```
 */
export const useCalendar = ({
  initialEvents = [],
  initialView = 'dayGridMonth',
  onAddEvent,
  onUpdateEvent,
  onDeleteEvent,
}: UseCalendarOptions = {}) => {
  // State
  const [events, setEvents] = useState<CalendarEvent[]>(initialEvents);
  const [currentView, setCurrentView] = useState<string>(initialView);
  const [calendarTitle, setCalendarTitle] = useState<string>('');
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

  // Refs
  const calendarRef = useRef<FullCalendar>(null);

  // Tạo sự kiện mẫu khi không có sự kiện nào
  const generateSampleEvents = useCallback((): CalendarEvent[] => {
    const today = new Date();
    const startOfToday = startOfDay(today);

    return [
      {
        id: '1',
        title: 'Họp nhóm',
        start: addHours(startOfToday, 10),
        end: addHours(startOfToday, 12),
        description: 'Cuộc họp nhóm hàng tuần để thảo luận về tiến độ dự án',
        location: 'Phòng họp A',
        className: 'calendar-event-meeting',
        extendedProps: {
          type: 'meeting',
        },
      },
      // Thêm các sự kiện mẫu khác nếu cần
    ];
  }, []);

  // Cập nhật tiêu đề calendar khi view thay đổi
  useEffect(() => {
    const updateTitle = () => {
      if (calendarRef.current) {
        const api = calendarRef.current.getApi();
        setCalendarTitle(api.view.title);
      }
    };

    // Cập nhật tiêu đề sau khi calendar được render
    setTimeout(updateTitle, 100);
  }, [currentView]);

  // Xử lý khi chọn ngày/khoảng thời gian
  const handleDateSelect = useCallback(
    (selectInfo: DateSelectArg) => {
      const calendarApi = selectInfo.view.calendar;
      calendarApi.unselect(); // Bỏ chọn date range

      // Tạo sự kiện mới
      const newEvent: CalendarEvent = {
        id: uuidv4(),
        title: 'Sự kiện mới',
        start: selectInfo.startStr,
        end: selectInfo.endStr,
        allDay: selectInfo.allDay,
        extendedProps: {
          type: 'meeting',
        },
      };

      setSelectedEvent(newEvent);

      // Thêm sự kiện mới vào state
      setEvents(prev => [...prev, newEvent]);

      // Gọi callback nếu có
      if (onAddEvent) {
        onAddEvent(newEvent);
      }
    },
    [onAddEvent]
  );

  // Xử lý khi click vào sự kiện
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      const event = events.find(e => e.id === clickInfo.event.id);
      if (event) {
        setSelectedEvent(event);
      }
    },
    [events]
  );

  // Xử lý khi kéo/thả hoặc resize sự kiện
  const handleEventChange = useCallback(
    (changeInfo: EventChangeArg) => {
      const updatedEvent: CalendarEvent = {
        ...events.find(e => e.id === changeInfo.event.id)!,
        start: changeInfo.event.startStr,
        end: changeInfo.event.endStr,
        allDay: changeInfo.event.allDay,
      };

      // Cập nhật sự kiện trong state
      setEvents(prev => prev.map(e => (e.id === updatedEvent.id ? updatedEvent : e)));

      // Gọi callback nếu có
      if (onUpdateEvent) {
        onUpdateEvent(updatedEvent);
      }
    },
    [events, onUpdateEvent]
  );

  // Xử lý khi thay đổi view
  const handleViewChange = useCallback((view: string) => {
    setCurrentView(view);
    if (calendarRef.current) {
      const api = calendarRef.current.getApi();
      api.changeView(view);
      setCalendarTitle(api.view.title);
    }
  }, []);

  // Các hàm điều hướng
  const handlePrev = useCallback(() => {
    if (calendarRef.current) {
      const api = calendarRef.current.getApi();
      api.prev();
      setCalendarTitle(api.view.title);
    }
  }, []);

  const handleNext = useCallback(() => {
    if (calendarRef.current) {
      const api = calendarRef.current.getApi();
      api.next();
      setCalendarTitle(api.view.title);
    }
  }, []);

  const handleToday = useCallback(() => {
    if (calendarRef.current) {
      const api = calendarRef.current.getApi();
      api.today();
      setCalendarTitle(api.view.title);
    }
  }, []);

  // Thêm sự kiện mới
  const addEvent = useCallback(
    (event: Partial<CalendarEvent>) => {
      const newEvent: CalendarEvent = {
        id: uuidv4(),
        title: event.title || 'Sự kiện mới',
        start: event.start || new Date(),
        end: event.end,
        allDay: event.allDay || false,
        description: event.description,
        location: event.location,
        className: event.className || `calendar-event-${event.extendedProps?.type || 'default'}`,
        extendedProps: event.extendedProps || { type: 'default' },
      };

      setEvents(prev => [...prev, newEvent]);

      if (onAddEvent) {
        onAddEvent(newEvent);
      }

      return newEvent;
    },
    [onAddEvent]
  );

  // Cập nhật sự kiện
  const updateEvent = useCallback(
    (eventId: string, updates: Partial<CalendarEvent>) => {
      setEvents(prev =>
        prev.map(event => {
          if (event.id === eventId) {
            const updatedEvent = { ...event, ...updates };

            if (onUpdateEvent) {
              onUpdateEvent(updatedEvent);
            }

            return updatedEvent;
          }
          return event;
        })
      );
    },
    [onUpdateEvent]
  );

  // Xóa sự kiện
  const deleteEvent = useCallback(
    (eventId: string) => {
      setEvents(prev => prev.filter(event => event.id !== eventId));

      if (onDeleteEvent) {
        onDeleteEvent(eventId);
      }
    },
    [onDeleteEvent]
  );

  return {
    events: events.length > 0 ? events : generateSampleEvents(),
    currentView,
    calendarRef,
    calendarTitle,
    selectedEvent,
    setSelectedEvent,
    handleDateSelect,
    handleEventClick,
    handleEventChange,
    handleViewChange,
    handlePrev,
    handleNext,
    handleToday,
    addEvent,
    updateEvent,
    deleteEvent,
  };
};

export default useCalendar;
