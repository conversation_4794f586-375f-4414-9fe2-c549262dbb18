import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button } from '@/shared/components/common';

import ReminderSelector from './ReminderSelector';
import { Reminder } from './types';

/**
 * Component demo cho ReminderSelector
 */
const ReminderSelectorDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho danh sách nhắc nhở
  const [reminders, setReminders] = useState<Reminder[]>([]);

  // Reset lựa chọn
  const handleReset = () => {
    setReminders([]);
  };

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('calendar.reminderSelectorDemo', 'Demo ReminderSelector')}
      </Typography>

      <Card className="p-4">
        <Typography variant="h3" className="mb-4">
          {t('calendar.reminderOptions', 'T<PERSON>y chọn nhắc nhở')}
        </Typography>

        <ReminderSelector
          reminders={reminders}
          onChange={setReminders}
          label={t('calendar.reminders', 'Nhắc nhở')}
        />

        {reminders.length > 0 && (
          <div className="mt-6 p-3 bg-card-muted rounded-md">
            <Typography variant="h4" className="mb-2">
              {t('calendar.selectedReminders', 'Nhắc nhở đã chọn')} ({reminders.length}):
            </Typography>
            <pre className="text-sm overflow-auto">{JSON.stringify(reminders, null, 2)}</pre>
          </div>
        )}

        <div className="mt-6 flex justify-center">
          <Button variant="outline" onClick={handleReset} className="px-4 py-2">
            {t('common.reset', 'Đặt lại')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ReminderSelectorDemo;
