import React from 'react';
import { useTranslation } from 'react-i18next';

import { Avatar, Grid } from '@/shared/components/common';

import { ComponentDemo } from '../components';

const AvatarPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('components.avatar.title', 'Avatar')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t(
            'components.avatar.description',
            'Avatar component hiển thị hình ảnh đại diện của người dùng hoặc thực thể.'
          )}
        </p>
      </div>

      <ComponentDemo
        title={t('components.avatar.basic.title', 'Basic Avatar')}
        description={t(
          'components.avatar.basic.description',
          'Avatar cơ bản với các kích thước kh<PERSON>c nhau.'
        )}
        code={`import { Avatar } from '@/shared/components/common';

// Extra small avatar
<Avatar src="https://i.pravatar.cc/300?img=1" alt="User" size="xs" />

// Small avatar
<Avatar src="https://i.pravatar.cc/300?img=2" alt="User" size="sm" />

// Medium avatar (default)
<Avatar src="https://i.pravatar.cc/300?img=3" alt="User" size="md" />

// Large avatar
<Avatar src="https://i.pravatar.cc/300?img=4" alt="User" size="lg" />

// Extra large avatar
<Avatar src="https://i.pravatar.cc/300?img=5" alt="User" size="xl" />`}
      >
        <div className="flex items-center space-x-4">
          <Avatar src="https://i.pravatar.cc/300?img=1" alt="User" size="xs" />
          <Avatar src="https://i.pravatar.cc/300?img=2" alt="User" size="sm" />
          <Avatar src="https://i.pravatar.cc/300?img=3" alt="User" size="md" />
          <Avatar src="https://i.pravatar.cc/300?img=4" alt="User" size="lg" />
          <Avatar src="https://i.pravatar.cc/300?img=5" alt="User" size="xl" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.avatar.status.title', 'Avatar with Status')}
        description={t(
          'components.avatar.status.description',
          'Avatar với các trạng thái khác nhau.'
        )}
        code={`import { Avatar } from '@/shared/components/common';

// Avatar with online status
<Avatar src="https://i.pravatar.cc/300?img=6" alt="User" status="online" />

// Avatar with offline status
<Avatar src="https://i.pravatar.cc/300?img=7" alt="User" status="offline" />

// Avatar with away status
<Avatar src="https://i.pravatar.cc/300?img=8" alt="User" status="away" />

// Avatar with busy status
<Avatar src="https://i.pravatar.cc/300?img=9" alt="User" status="busy" />`}
      >
        <div className="flex items-center space-x-4">
          <Avatar src="https://i.pravatar.cc/300?img=6" alt="User" status="online" />
          <Avatar src="https://i.pravatar.cc/300?img=7" alt="User" status="offline" />
          <Avatar src="https://i.pravatar.cc/300?img=8" alt="User" status="away" />
          <Avatar src="https://i.pravatar.cc/300?img=9" alt="User" status="busy" />
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.avatar.group.title', 'Avatar Group')}
        description={t(
          'components.avatar.group.description',
          'Nhóm các avatar với hiệu ứng chồng lên nhau.'
        )}
        code={`import { Avatar } from '@/shared/components/common';

// Avatar group
<div className="flex -space-x-2">
  <Avatar src="https://i.pravatar.cc/300?img=10" alt="User 1" className="border-2 border-white dark:border-gray-800" />
  <Avatar src="https://i.pravatar.cc/300?img=11" alt="User 2" className="border-2 border-white dark:border-gray-800" />
  <Avatar src="https://i.pravatar.cc/300?img=12" alt="User 3" className="border-2 border-white dark:border-gray-800" />
  <Avatar src="https://i.pravatar.cc/300?img=13" alt="User 4" className="border-2 border-white dark:border-gray-800" />
  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 border-2 border-white dark:border-gray-800 text-xs font-medium">
    +5
  </div>
</div>`}
      >
        <div className="flex -space-x-2">
          <Avatar
            src="https://i.pravatar.cc/300?img=10"
            alt="User 1"
            className="border-2 border-white dark:border-gray-800"
          />
          <Avatar
            src="https://i.pravatar.cc/300?img=11"
            alt="User 2"
            className="border-2 border-white dark:border-gray-800"
          />
          <Avatar
            src="https://i.pravatar.cc/300?img=12"
            alt="User 3"
            className="border-2 border-white dark:border-gray-800"
          />
          <Avatar
            src="https://i.pravatar.cc/300?img=13"
            alt="User 4"
            className="border-2 border-white dark:border-gray-800"
          />
          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 border-2 border-white dark:border-gray-800 text-xs font-medium">
            +5
          </div>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.avatar.usage.title', 'Avatar Usage Examples')}
        description={t(
          'components.avatar.usage.description',
          'Các ví dụ về cách sử dụng Avatar trong giao diện.'
        )}
        code={`import { Avatar } from '@/shared/components/common';

// User profile card
<div className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
  <Avatar src="https://i.pravatar.cc/300?img=14" alt="User" size="lg" status="online" />
  <div className="ml-4">
    <h3 className="font-medium">John Doe</h3>
    <p className="text-sm text-gray-500 dark:text-gray-400">Product Designer</p>
  </div>
</div>

// Comment with avatar
<div className="flex space-x-3">
  <Avatar src="https://i.pravatar.cc/300?img=15" alt="User" size="sm" />
  <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
    <p className="font-medium">Jane Smith</p>
    <p className="text-sm">Great work on this project!</p>
  </div>
</div>`}
      >
        <Grid columns={{ sm: 1, md: 2 }} columnGap="md" rowGap="md">
          <div className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
            <Avatar src="https://i.pravatar.cc/300?img=14" alt="User" size="lg" status="online" />
            <div className="ml-4">
              <h3 className="font-medium">John Doe</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Product Designer</p>
            </div>
          </div>

          <div className="flex space-x-3">
            <Avatar src="https://i.pravatar.cc/300?img=15" alt="User" size="sm" />
            <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
              <p className="font-medium">Jane Smith</p>
              <p className="text-sm">Great work on this project!</p>
            </div>
          </div>
        </Grid>
      </ComponentDemo>
    </div>
  );
};

export default AvatarPage;
