/**
 * Đơn vị thời gian cho nhắc nhở
 */
export enum ReminderTimeUnit {
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
}

/**
 * Phương thức nhắc nhở
 */
export enum ReminderMethod {
  EMAIL = 'email',
  NOTIFICATION = 'notification',
}

/**
 * Interface cho nhắc nhở
 */
export interface Reminder {
  /**
   * ID của nhắc nhở
   */
  id: string;

  /**
   * Thời gian nhắc trước
   */
  time: number;

  /**
   * Đơn vị thời gian
   */
  unit: ReminderTimeUnit;

  /**
   * Phương thức nhắc nhở
   */
  method: ReminderMethod;

  /**
   * Nội dung nhắc nhở (tùy chọn)
   */
  message?: string;
}

/**
 * Các giá trị mặc định cho nhắc nhở
 */
export const DEFAULT_REMINDERS: Omit<Reminder, 'id'>[] = [
  { time: 5, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
  { time: 15, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
  { time: 30, unit: ReminderTimeUnit.MINUTE, method: ReminderMethod.NOTIFICATION },
  { time: 1, unit: ReminderTimeUnit.HOUR, method: ReminderMethod.NOTIFICATION },
  { time: 2, unit: ReminderTimeUnit.HOUR, method: ReminderMethod.NOTIFICATION },
  { time: 1, unit: ReminderTimeUnit.DAY, method: ReminderMethod.EMAIL },
  { time: 2, unit: ReminderTimeUnit.DAY, method: ReminderMethod.EMAIL },
];

/**
 * Chuyển đổi nhắc nhở thành chuỗi mô tả
 */
export const reminderToString = (reminder: Reminder, locale = 'vi'): string => {
  const { time, unit, method } = reminder;

  // Định dạng thời gian
  let timeStr = '';
  switch (unit) {
    case ReminderTimeUnit.MINUTE:
      timeStr = locale === 'vi' ? `${time} phút` : `${time} minute${time !== 1 ? 's' : ''}`;
      break;
    case ReminderTimeUnit.HOUR:
      timeStr = locale === 'vi' ? `${time} giờ` : `${time} hour${time !== 1 ? 's' : ''}`;
      break;
    case ReminderTimeUnit.DAY:
      timeStr = locale === 'vi' ? `${time} ngày` : `${time} day${time !== 1 ? 's' : ''}`;
      break;
  }

  // Định dạng phương thức
  let methodStr = '';
  switch (method) {
    case ReminderMethod.EMAIL:
      methodStr = locale === 'vi' ? 'email' : 'email';
      break;
    case ReminderMethod.NOTIFICATION:
      methodStr = locale === 'vi' ? 'thông báo' : 'notification';
      break;
  }

  return locale === 'vi'
    ? `${timeStr} trước (qua ${methodStr})`
    : `${timeStr} before (via ${methodStr})`;
};
