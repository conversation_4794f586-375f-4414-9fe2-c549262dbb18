import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

import { Button, Icon, Select, Input, Typography, Chip } from '@/shared/components/common';

import {
  Reminder,
  ReminderTimeUnit,
  ReminderMethod,
  DEFAULT_REMINDERS,
  reminderToString,
} from './types';

/**
 * Props cho ReminderSelector
 */
export interface ReminderSelectorProps {
  /**
   * Danh sách nhắc nhở hiện tại
   */
  reminders: Reminder[];

  /**
   * Callback khi thay đổi danh sách nhắc nhở
   */
  onChange: (reminders: Reminder[]) => void;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * CSS class bổ sung
   */
  className?: string;

  /**
   * Số lượng nhắc nhở tối đa
   */
  maxReminders?: number;
}

/**
 * Component ReminderSelector - <PERSON><PERSON><PERSON> c<PERSON>u hình nhắc nhở
 *
 * @example
 * ```tsx
 * const [reminders, setReminders] = useState<Reminder[]>([]);
 *
 * <ReminderSelector
 *   reminders={reminders}
 *   onChange={setReminders}
 * />
 * ```
 */
const ReminderSelector: React.FC<ReminderSelectorProps> = ({
  reminders,
  onChange,
  label,
  disabled = false,
  className = '',
  maxReminders = 5,
}) => {
  const { t } = useTranslation();

  // State cho nhắc nhở mới
  const [newReminderTime, setNewReminderTime] = useState<number>(15);
  const [newReminderUnit, setNewReminderUnit] = useState<ReminderTimeUnit>(ReminderTimeUnit.MINUTE);
  const [newReminderMethod, setNewReminderMethod] = useState<ReminderMethod>(
    ReminderMethod.NOTIFICATION
  );

  // Xử lý khi thêm nhắc nhở
  const handleAddReminder = () => {
    if (reminders.length >= maxReminders) {return;}

    // Kiểm tra xem đã có nhắc nhở tương tự chưa
    const exists = reminders.some(
      r =>
        r.time === newReminderTime && r.unit === newReminderUnit && r.method === newReminderMethod
    );

    if (exists) {return;}

    const newReminder: Reminder = {
      id: uuidv4(),
      time: newReminderTime,
      unit: newReminderUnit,
      method: newReminderMethod,
    };

    onChange([...reminders, newReminder]);
  };

  // Xử lý khi xóa nhắc nhở
  const handleRemoveReminder = (id: string) => {
    onChange(reminders.filter(r => r.id !== id));
  };

  // Xử lý khi thêm nhắc nhở mặc định
  const handleAddDefaultReminder = (defaultReminder: Omit<Reminder, 'id'>) => {
    // Kiểm tra xem đã có nhắc nhở tương tự chưa
    const exists = reminders.some(
      r =>
        r.time === defaultReminder.time &&
        r.unit === defaultReminder.unit &&
        r.method === defaultReminder.method
    );

    if (exists) {return;}

    const newReminder: Reminder = {
      id: uuidv4(),
      ...defaultReminder,
    };

    onChange([...reminders, newReminder]);
  };

  return (
    <div className={`reminder-selector ${className}`}>
      {/* Label */}
      {label && (
        <Typography variant="body2" className="mb-2 font-medium">
          {label}
        </Typography>
      )}

      {/* Danh sách nhắc nhở hiện tại */}
      {reminders.length > 0 && (
        <div className="mb-4 space-y-2">
          <Typography variant="body2" className="font-medium">
            {t('calendar.currentReminders', 'Nhắc nhở hiện tại')}:
          </Typography>

          <div className="space-y-2">
            {reminders.map(reminder => (
              <div
                key={reminder.id}
                className="flex items-center justify-between p-2 bg-card-muted rounded-md border border-border"
              >
                <div className="flex items-center">
                  <Icon
                    name={reminder.method === ReminderMethod.EMAIL ? 'mail' : 'info'}
                    size="sm"
                    className="mr-2 text-muted"
                  />
                  <span>{reminderToString(reminder)}</span>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveReminder(reminder.id)}
                  disabled={disabled}
                  className="p-1 hover:bg-card-hover rounded-full"
                >
                  <Icon name="x" size="sm" className="text-muted" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Form thêm nhắc nhở mới */}
      {reminders.length < maxReminders && (
        <div className="mb-4">
          <Typography variant="body2" className="mb-2 font-medium">
            {t('calendar.addReminder', 'Thêm nhắc nhở')}:
          </Typography>

          <div className="flex flex-wrap items-end gap-2">
            <div className="flex-1 min-w-[120px]">
              <Input
                type="number"
                min={1}
                max={99}
                value={newReminderTime.toString()}
                onChange={e => setNewReminderTime(parseInt(e.target.value) || 1)}
                disabled={disabled}
                className="w-full"
              />
            </div>

            <div className="flex-1 min-w-[120px]">
              <Select
                value={newReminderUnit}
                onChange={val => setNewReminderUnit(val as ReminderTimeUnit)}
                options={[
                  { value: ReminderTimeUnit.MINUTE, label: t('calendar.minutes', 'Phút') },
                  { value: ReminderTimeUnit.HOUR, label: t('calendar.hours', 'Giờ') },
                  { value: ReminderTimeUnit.DAY, label: t('calendar.days', 'Ngày') },
                ]}
                disabled={disabled}
                fullWidth
              />
            </div>

            <div className="flex-1 min-w-[150px]">
              <Select
                value={newReminderMethod}
                onChange={val => setNewReminderMethod(val as ReminderMethod)}
                options={[
                  {
                    value: ReminderMethod.NOTIFICATION,
                    label: t('calendar.notification', 'Thông báo'),
                  },
                  { value: ReminderMethod.EMAIL, label: t('calendar.email', 'Email') },
                ]}
                disabled={disabled}
                fullWidth
              />
            </div>

            <Button
              variant="primary"
              size="sm"
              onClick={handleAddReminder}
              disabled={disabled}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('calendar.add', 'Thêm')}
            </Button>
          </div>
        </div>
      )}

      {/* Nhắc nhở mặc định */}
      <div>
        <Typography variant="body2" className="mb-2 font-medium">
          {t('calendar.defaultReminders', 'Nhắc nhở mặc định')}:
        </Typography>

        <div className="flex flex-wrap gap-2">
          {DEFAULT_REMINDERS.map((reminder, index) => {
            // Kiểm tra xem đã có nhắc nhở tương tự chưa
            const exists = reminders.some(
              r =>
                r.time === reminder.time && r.unit === reminder.unit && r.method === reminder.method
            );

            return (
              <Chip
                key={index}
                variant={exists ? 'info' : 'primary'}
                className={`${exists ? 'opacity-50' : ''}`}
                leftIconName={reminder.method === ReminderMethod.EMAIL ? 'mail' : 'info'}
                disabled={exists || disabled || reminders.length >= maxReminders}
                onClick={e => {
                  e.preventDefault();
                  if (!exists && !disabled && reminders.length < maxReminders) {
                    handleAddDefaultReminder(reminder);
                  }
                }}
                size="sm"
              >
                {reminderToString(reminder as Reminder)}
              </Chip>
            );
          })}
        </div>
      </div>

      {/* Thông báo giới hạn */}
      {reminders.length >= maxReminders && (
        <div className="mt-2 text-sm text-warning">
          <Icon name="warning" size="sm" className="inline-block mr-1" />
          {t('calendar.maxRemindersReached', 'Đã đạt giới hạn số lượng nhắc nhở ({{max}})', {
            max: maxReminders,
          })}
        </div>
      )}
    </div>
  );
};

export default ReminderSelector;
