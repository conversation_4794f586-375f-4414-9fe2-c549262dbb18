import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, SearchInputWithImage, Icon } from '@/shared/components/common';
import { SearchItem } from '@/shared/types/search-input-with-image.types';

/**
 * <PERSON><PERSON> dụ sử dụng component SearchInputWithImage
 */
const SearchInputWithImageExample: React.FC = () => {
  const { t } = useTranslation();
  const [selectedValue, setSelectedValue] = useState<string | number>('');
  const [selectedItem, setSelectedItem] = useState<SearchItem | null>(null);

  // Danh sách người dùng mẫu
  const users: SearchItem[] = [
    {
      id: 1,
      name: '<PERSON>uy<PERSON><PERSON>ăn <PERSON>',
      imageUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
      description: 'Frontend Developer',
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      imageUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
      description: 'UI/UX Designer',
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      imageUrl: 'https://randomuser.me/api/portraits/men/3.jpg',
      description: 'Backend Developer',
    },
    {
      id: 4,
      name: 'Phạm Thị D',
      imageUrl: 'https://randomuser.me/api/portraits/women/4.jpg',
      description: 'Product Manager',
    },
    {
      id: 5,
      name: 'Hoàng Văn E',
      imageUrl: 'https://randomuser.me/api/portraits/men/5.jpg',
      description: 'DevOps Engineer',
    },
    {
      id: 6,
      name: 'Ngô Thị F',
      icon: <Icon name="user" size="md" />,
      description: 'QA Engineer',
    },
  ];

  // Danh sách sản phẩm mẫu
  const products: SearchItem[] = [
    {
      id: 'p1',
      name: 'Laptop Dell XPS 13',
      imageUrl: 'https://picsum.photos/id/0/200',
      description: '16GB RAM, 512GB SSD',
    },
    {
      id: 'p2',
      name: 'iPhone 13 Pro',
      imageUrl: 'https://picsum.photos/id/1/200',
      description: '256GB, Graphite',
    },
    {
      id: 'p3',
      name: 'Samsung Galaxy S22',
      imageUrl: 'https://picsum.photos/id/2/200',
      description: '128GB, Phantom Black',
    },
    {
      id: 'p4',
      name: 'MacBook Pro M1',
      imageUrl: 'https://picsum.photos/id/3/200',
      description: '8GB RAM, 512GB SSD',
    },
    {
      id: 'p5',
      name: 'iPad Air',
      imageUrl: 'https://picsum.photos/id/4/200',
      description: '64GB, Space Gray',
    },
  ];

  // Xử lý khi chọn item
  const handleChange = (value: string | number, item: SearchItem) => {
    setSelectedValue(value);
    setSelectedItem(item);
  };

  return (
    <div className="space-y-8">
      <Card className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('components.searchInputWithImage.userExample', 'Tìm kiếm người dùng')}
        </Typography>

        <div className="max-w-md mb-4">
          <SearchInputWithImage
            label={t('components.searchInputWithImage.userLabel', 'Chọn người dùng')}
            placeholder={t(
              'components.searchInputWithImage.userPlaceholder',
              'Nhập tên người dùng...'
            )}
            items={users}
            value={selectedValue}
            onChange={handleChange}
            fullWidth
          />
        </div>

        {selectedItem && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-dark-lighter rounded-md">
            <Typography variant="h6" className="mb-2">
              {t('components.searchInputWithImage.selectedUser', 'Người dùng đã chọn')}
            </Typography>
            <div className="flex items-center">
              {selectedItem.imageUrl ? (
                <img
                  src={selectedItem.imageUrl}
                  alt={selectedItem.name}
                  className="w-10 h-10 rounded-full mr-3"
                />
              ) : selectedItem.icon ? (
                <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-dark-light flex items-center justify-center mr-3">
                  {selectedItem.icon}
                </div>
              ) : (
                <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-dark-light flex items-center justify-center mr-3">
                  <Icon name="user" size="md" className="text-gray-500" />
                </div>
              )}
              <div>
                <Typography variant="subtitle1">{selectedItem.name}</Typography>
                {selectedItem.description && (
                  <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                    {selectedItem.description}
                  </Typography>
                )}
              </div>
            </div>
          </div>
        )}
      </Card>

      <Card className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('components.searchInputWithImage.productExample', 'Tìm kiếm sản phẩm')}
        </Typography>

        <div className="max-w-md mb-4">
          <SearchInputWithImage
            label={t('components.searchInputWithImage.productLabel', 'Chọn sản phẩm')}
            placeholder={t(
              'components.searchInputWithImage.productPlaceholder',
              'Nhập tên sản phẩm...'
            )}
            items={products}
            value={selectedValue}
            onChange={handleChange}
            imageSize="lg"
            fullWidth
          />
        </div>
      </Card>

      <Card className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('components.searchInputWithImage.variants', 'Các biến thể')}
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.smallSize', 'Kích thước nhỏ')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              size="sm"
              imageSize="sm"
              fullWidth
            />
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.largeSize', 'Kích thước lớn')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              size="lg"
              imageSize="lg"
              fullWidth
            />
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.withError', 'Có lỗi')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              error={t(
                'components.searchInputWithImage.errorMessage',
                'Vui lòng chọn một người dùng'
              )}
              fullWidth
            />
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.withHelper', 'Có text hỗ trợ')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              helperText={t(
                'components.searchInputWithImage.helperText',
                'Nhập tên để tìm kiếm người dùng'
              )}
              fullWidth
            />
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.disabled', 'Bị vô hiệu hóa')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              disabled
              fullWidth
            />
          </div>

          <div>
            <Typography variant="subtitle1" className="mb-2">
              {t('components.searchInputWithImage.noSearchIcon', 'Không có icon tìm kiếm')}
            </Typography>
            <SearchInputWithImage
              placeholder={t('components.searchInputWithImage.search', 'Tìm kiếm...')}
              items={users}
              showSearchIcon={false}
              fullWidth
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SearchInputWithImageExample;
