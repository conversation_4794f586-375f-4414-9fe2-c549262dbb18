import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

import { UserStatus } from '../enum/user-status.enum';

/**
 * Entity representing users in the system
 */
@Entity('users')
export class User {
  /**
   * Unique identifier for the user
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * User's login username
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  username: string;

  /**
   * User's email address
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  email: string;

  /**
   * User's encrypted password
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  password: string;

  /**
   * User's full name
   */
  @Column({ name: 'full_name', type: 'varchar', length: 255, nullable: true })
  fullName: string | null;

  /**
   * ID of the department the user belongs to
   */
  @Column({ name: 'department_id', type: 'integer', nullable: true })
  departmentId: number | null;

  /**
   * User account status
   */
  @Column({ type: 'enum', enum: UserStatus, nullable: true })
  status: UserStatus | null;

  /**
   * User's position in the organization
   */
  @Column({ type: 'varchar', length: 255, nullable: true })
  position: string | null;

  /**
   * Creation timestamp (in milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * User's home address
   */
  @Column({ type: 'varchar', length: 500, nullable: true })
  address: string | null;

  /**
   * User's contact phone number
   */
  @Column({ name: 'phone_number', type: 'varchar', length: 20, nullable: true })
  phoneNumber: string | null;

  /**
   * User's birth date
   */
  @Column({ name: 'birth_date', type: 'date', nullable: true })
  birthDate: Date | null;

  /**
   * User's gender
   */
  @Column({ type: 'varchar', length: 10, nullable: true })
  gender: string | null;

  /**
   * URL to user's avatar image
   */
  @Column({ name: 'avatar_url', type: 'varchar', length: 500, nullable: true })
  avatarUrl: string | null;

  /**
   * User's ID card number
   */
  @Column({ name: 'id_card_number', type: 'varchar', length: 20, nullable: true })
  idCardNumber: string | null;

  /**
   * Date when ID card was issued
   */
  @Column({ name: 'id_card_issue_date', type: 'date', nullable: true })
  idCardIssueDate: Date | null;

  /**
   * Place where ID card was issued
   */
  @Column({ name: 'id_card_issue_place', type: 'varchar', length: 255, nullable: true })
  idCardIssuePlace: string | null;

  /**
   * User's bank account number
   */
  @Column({ name: 'bank_account_number', type: 'varchar', length: 50, nullable: true })
  bankAccountNumber: string | null;

  /**
   * Name of user's bank
   */
  @Column({ name: 'bank_name', type: 'varchar', length: 255, nullable: true })
  bankName: string | null;

  /**
   * User's tax code
   */
  @Column({ name: 'tax_code', type: 'varchar', length: 50, nullable: true })
  taxCode: string | null;

  /**
   * User's social insurance number
   */
  @Column({ name: 'insurance_number', type: 'varchar', length: 50, nullable: true })
  insuranceNumber: string | null;

  /**
   * Type of employee (part-time or full-time)
   */
  @Column({ name: 'user_type', type: 'varchar', length: 100, nullable: true })
  userType: string | null;

  /**
   * ID of the company/organization that owns this record
   */
  @Column({ name: 'tenant_id', type: 'integer', nullable: true })
  tenantId: number | null;
}
