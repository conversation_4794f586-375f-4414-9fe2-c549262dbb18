import axios from 'axios';

import { AppException } from '@/common/exceptions/app.exception';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { SocialProvider } from '../../enum';
import { AUTH_ERROR_CODE } from '../../errors/auth-error.code';

import { ISocialAuthProvider, SocialUserInfo } from './social-provider.interface';

/**
 * Service xử lý xác thực qua Zalo
 */
@Injectable()
export class ZaloAuthService implements ISocialAuthProvider {
  private readonly logger = new Logger(ZaloAuthService.name);
  private readonly zaloApiUrl = 'https://graph.zalo.me/v2.0/me';
  private readonly zaloAccessTokenUrl = 'https://oauth.zaloapp.com/v4/access_token';

  constructor(private readonly configService: ConfigService) {}

  /**
   * L<PERSON>y thông tin người dùng từ Zalo bằng access token
   * @param accessToken Access token từ Zalo
   * @returns Thông tin người dùng
   */
  async getUserInfo(accessToken: string): Promise<SocialUserInfo> {
    try {
      // Xác thực token trước khi lấy thông tin
      const isValid = await this.verifyToken(accessToken);
      if (!isValid) {
        throw new AppException(
          AUTH_ERROR_CODE.INVALID_SOCIAL_TOKEN,
          'Zalo access token không hợp lệ hoặc đã hết hạn',
        );
      }

      // Gọi API Zalo để lấy thông tin người dùng
      const response = await axios.get(`${this.zaloApiUrl}`, {
        params: {
          fields: 'id,name,picture',
        },
        headers: {
          'access_token': accessToken,
        },
      });

      const userData = response.data;

      // Chuyển đổi dữ liệu từ Zalo sang định dạng chung
      return {
        id: userData.id,
        name: userData.name,
        displayName: userData.name,
        photoUrl: userData.picture?.data?.url,
        provider: SocialProvider.ZALO,
        accessToken,
        rawProfile: userData,
      };
    } catch (error) {
      this.logger.error(`Error getting user info from Zalo: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AUTH_ERROR_CODE.SOCIAL_AUTH_FAILED,
        `Không thể lấy thông tin người dùng từ Zalo: ${error.message}`,
      );
    }
  }

  /**
   * Xác thực Zalo access token
   * @param accessToken Access token từ Zalo
   * @returns True nếu token hợp lệ, false nếu không
   */
  async verifyToken(accessToken: string): Promise<boolean> {
    try {
      // Gọi API Zalo để lấy thông tin cơ bản, nếu token không hợp lệ sẽ trả về lỗi
      const response = await axios.get(`${this.zaloApiUrl}`, {
        params: {
          fields: 'id',
        },
        headers: {
          'access_token': accessToken,
        },
      });
      
      // Nếu có ID người dùng, token hợp lệ
      return !!response.data.id;
    } catch (error) {
      this.logger.error(`Error verifying Zalo token: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy loại nhà cung cấp mạng xã hội
   * @returns Zalo
   */
  getProvider(): SocialProvider {
    return SocialProvider.ZALO;
  }
}
