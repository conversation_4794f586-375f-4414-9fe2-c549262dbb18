import React, { useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

import { Button, Icon, Typography } from '@/shared/components/common';

import Progress from '../common/Progress';

import {
  AttachmentFile,
  UploadStatus,
  ACCEPTED_FILE_TYPES,
  formatFileSize,
  getFileIcon,
} from './types';

/**
 * Props cho FileUploader
 */
export interface FileUploaderProps {
  /**
   * Danh sách tệp đã tải lên
   */
  files: AttachmentFile[];

  /**
   * Callback khi thay đổi danh sách tệp
   */
  onChange: (files: AttachmentFile[]) => void;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * CSS class bổ sung
   */
  className?: string;

  /**
   * Kích thước tối đa của tệp (MB)
   */
  maxSize?: number;

  /**
   * <PERSON><PERSON> lượng tệp tối đa
   */
  maxFiles?: number;

  /**
   * <PERSON><PERSON>c loại tệp được chấp nhận
   */
  accept?: string[];

  /**
   * Cho phép kéo thả
   */
  dragDrop?: boolean;

  /**
   * Callback khi tải lên tệp
   */
  onUpload?: (file: File) => Promise<string>;
}

/**
 * Component FileUploader - Tải lên tệp đính kèm
 *
 * @example
 * ```tsx
 * const [files, setFiles] = useState<AttachmentFile[]>([]);
 *
 * <FileUploader
 *   files={files}
 *   onChange={setFiles}
 *   maxSize={5}
 *   maxFiles={10}
 *   accept={ACCEPTED_FILE_TYPES.ALL}
 *   dragDrop={true}
 * />
 * ```
 */
const FileUploader: React.FC<FileUploaderProps> = ({
  files,
  onChange,
  label,
  disabled = false,
  className = '',
  maxSize = 5, // 5MB
  maxFiles = 10,
  accept = ACCEPTED_FILE_TYPES.ALL,
  dragDrop = true,
  onUpload,
}) => {
  const { t } = useTranslation();

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Xử lý khi click vào nút tải lên
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Xử lý khi chọn tệp
  const handleFileChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (!selectedFiles || selectedFiles.length === 0) {return;}

      // Kiểm tra số lượng tệp
      if (files.length + selectedFiles.length > maxFiles) {
        alert(
          t('calendar.maxFilesExceeded', 'Số lượng tệp vượt quá giới hạn ({{max}})', {
            max: maxFiles,
          })
        );
        return;
      }

      // Xử lý từng tệp
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];

        // Kiểm tra loại tệp
        if (accept.length > 0 && !accept.includes(file.type)) {
          alert(
            t('calendar.fileTypeNotAccepted', 'Loại tệp không được chấp nhận: {{type}}', {
              type: file.type,
            })
          );
          continue;
        }

        // Kiểm tra kích thước tệp
        if (file.size > maxSize * 1024 * 1024) {
          alert(
            t(
              'calendar.fileSizeExceeded',
              'Kích thước tệp vượt quá giới hạn ({{max}}MB): {{name}}',
              { max: maxSize, name: file.name }
            )
          );
          continue;
        }

        // Tạo đối tượng tệp mới
        const newFile: AttachmentFile = {
          id: uuidv4(),
          name: file.name,
          url: URL.createObjectURL(file),
          size: file.size,
          type: file.type,
          uploadedAt: new Date(),
          status: UploadStatus.IDLE,
          progress: 0,
        };

        // Thêm vào danh sách
        onChange([...files, newFile]);

        // Tải lên tệp nếu có callback
        if (onUpload) {
          try {
            // Cập nhật trạng thái tải lên
            onChange(
              files.map(f =>
                f.id === newFile.id ? { ...f, status: UploadStatus.UPLOADING, progress: 0 } : f
              )
            );

            // Giả lập tiến trình tải lên
            const simulateProgress = setInterval(() => {
              const updateProgress = (prevFiles: AttachmentFile[]): AttachmentFile[] => {
                const fileToUpdate = prevFiles.find((f: AttachmentFile) => f.id === newFile.id);
                if (
                  fileToUpdate &&
                  fileToUpdate.status === UploadStatus.UPLOADING &&
                  fileToUpdate.progress !== undefined &&
                  fileToUpdate.progress < 90
                ) {
                  return prevFiles.map((f: AttachmentFile) =>
                    f.id === newFile.id ? { ...f, progress: (f.progress || 0) + 10 } : f
                  );
                }
                return prevFiles;
              };
              onChange([...updateProgress(files)]);
            }, 300);

            // Gọi callback tải lên
            const url = await onUpload(file);

            // Dừng giả lập tiến trình
            clearInterval(simulateProgress);

            // Cập nhật URL và trạng thái
            onChange(
              files.map((f: AttachmentFile) =>
                f.id === newFile.id ? { ...f, url, status: UploadStatus.SUCCESS, progress: 100 } : f
              )
            );
          } catch (error) {
            // Xử lý lỗi
            onChange(
              files.map((f: AttachmentFile) =>
                f.id === newFile.id
                  ? {
                      ...f,
                      status: UploadStatus.ERROR,
                      error: (error as Error).message,
                      progress: 0,
                    }
                  : f
              )
            );
          }
        }
      }

      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    [accept, files, maxFiles, maxSize, onChange, onUpload, t]
  );

  // Xử lý khi xóa tệp
  const handleRemoveFile = (id: string) => {
    onChange(files.filter(file => file.id !== id));
  };

  // Xử lý khi kéo thả
  const handleDragEnter = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      if (!disabled && dragDrop) {
        setIsDragging(true);
      }
    },
    [disabled, dragDrop]
  );

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (disabled || !dragDrop) {return;}

      const droppedFiles = e.dataTransfer.files;
      if (!droppedFiles || droppedFiles.length === 0) {return;}

      // Tạo đối tượng giả lập input để sử dụng lại logic xử lý tệp
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.multiple = true;

      // Tạo đối tượng DataTransfer để chuyển đổi FileList thành File[]
      const dataTransfer = new DataTransfer();
      for (let i = 0; i < droppedFiles.length; i++) {
        dataTransfer.items.add(droppedFiles[i]);
      }

      // Gán FileList cho input
      fileInput.files = dataTransfer.files;

      // Giả lập sự kiện change
      const event = {
        target: fileInput,
      } as unknown as React.ChangeEvent<HTMLInputElement>;

      handleFileChange(event);
    },
    [disabled, dragDrop, handleFileChange]
  );

  return (
    <div className={`file-uploader ${className}`}>
      {/* Label */}
      {label && (
        <Typography variant="body2" className="mb-2 font-medium">
          {label}
        </Typography>
      )}

      {/* Khu vực kéo thả */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-4 text-center transition-colors
          ${isDragging ? 'border-primary bg-primary/5' : 'border-border'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onClick={!disabled ? handleUploadClick : undefined}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={accept.join(',')}
          onChange={handleFileChange}
          disabled={disabled}
          className="hidden"
        />

        <Icon name="upload" size="lg" className="mb-2 text-muted" />

        <Typography variant="body2" className="mb-1">
          {isDragging
            ? t('calendar.dropFilesHere', 'Thả tệp vào đây')
            : t('calendar.dragDropFiles', 'Kéo thả tệp vào đây hoặc click để chọn')}
        </Typography>

        <Typography variant="caption" className="text-muted">
          {t('calendar.maxFileSize', 'Kích thước tối đa: {{size}}MB', { size: maxSize })}
        </Typography>
      </div>

      {/* Danh sách tệp đã tải lên */}
      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          {files.map(file => (
            <div
              key={file.id}
              className="flex items-center justify-between p-2 bg-card-muted rounded-md border border-border"
            >
              <div className="flex items-center overflow-hidden">
                <Icon name={getFileIcon(file.type)} size="sm" className="mr-2 flex-shrink-0" />

                <div className="overflow-hidden">
                  <Typography variant="body2" className="truncate">
                    {file.name}
                  </Typography>

                  <Typography variant="caption" className="text-muted">
                    {formatFileSize(file.size)}
                  </Typography>
                </div>
              </div>

              <div className="flex items-center ml-2">
                {/* Hiển thị trạng thái tải lên */}
                {file.status === UploadStatus.UPLOADING && (
                  <div className="w-20 mr-2">
                    <Progress value={file.progress || 0} size="sm" />
                  </div>
                )}

                {/* Nút xóa */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={e => {
                    e.stopPropagation();
                    handleRemoveFile(file.id);
                  }}
                  disabled={disabled || file.status === UploadStatus.UPLOADING}
                  className="p-1 hover:bg-card-hover rounded-full"
                >
                  <Icon name="x" size="sm" className="text-muted" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Thông báo giới hạn */}
      {files.length >= maxFiles && (
        <div className="mt-2 text-sm text-warning">
          <Icon name="warning" size="sm" className="inline-block mr-1" />
          {t('calendar.maxFilesReached', 'Đã đạt giới hạn số lượng tệp ({{max}})', {
            max: maxFiles,
          })}
        </div>
      )}
    </div>
  );
};

export default FileUploader;
