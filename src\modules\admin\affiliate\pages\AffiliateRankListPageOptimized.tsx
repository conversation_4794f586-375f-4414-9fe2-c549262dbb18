import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';

import AffiliateRankForm from '../components/forms/AffiliateRankForm';
import { useAffiliateRankData } from '../hooks/useAffiliateData';
import {
  AffiliateRankDto,
  AffiliateRankStatus,
  AffiliateRankQueryDto,
} from '../types/affiliate.types';

/**
 * Trang quản lý cấp bậc affiliate sử dụng các hooks tối ưu
 */
const AffiliateRankListPageOptimized: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AffiliateRankDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'name',
        title: t('affiliate:rank.table.name'),
        dataIndex: 'name',
        width: '15%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('affiliate:rank.table.description'),
        dataIndex: 'description',
        width: '25%',
        sortable: true,
      },
      {
        key: 'minReferrals',
        title: t('affiliate:rank.table.minReferrals'),
        dataIndex: 'minReferrals',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}</div>;
        },
      },
      {
        key: 'commissionRate',
        title: t('affiliate:rank.table.commissionRate'),
        dataIndex: 'commissionRate',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}%</div>;
        },
      },
      {
        key: 'status',
        title: t('affiliate:rank.table.status'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as AffiliateRankStatus;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === AffiliateRankStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {status === AffiliateRankStatus.ACTIVE ? t('common:active') : t('common:inactive')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '15%',
        render: (_: unknown, record: AffiliateRankDto) => (
          <div className="flex space-x-2">
            <Tooltip content={t('common:view')} position="top">
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => console.log('View', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:edit')} position="top">
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => console.log('Edit', record.id)}
              />
            </Tooltip>
            <Tooltip content={t('common:delete')} position="top">
              <IconCard
                icon="trash"
                variant="default"
                size="sm"
                onClick={() => console.log('Delete', record.id)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: AffiliateRankStatus.ACTIVE },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: AffiliateRankStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AffiliateRankQueryDto => {
    const queryParams: AffiliateRankQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AffiliateRankStatus;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AffiliateRankDto, AffiliateRankQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Lấy hook từ useAffiliateRankData
  const { useRanks } = useAffiliateRankData();

  // Gọi API lấy danh sách rank với queryParams từ dataTable
  const { data: rankData, isLoading } = useRanks(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AffiliateRankStatus.ACTIVE]: t('common:active'),
      [AffiliateRankStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <AffiliateRankForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={rankData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: rankData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: rankData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AffiliateRankListPageOptimized;
