import { useMutation } from '@tanstack/react-query';

import { CompanyAuthService } from '../services';
import {
  CompanyRegisterRequest,
  CompanyVerifyEmailRequest,
  CompanyLoginRequest,
} from '../types/company-auth.types';

/**
 * Hook xử lý các thao tác xác thực công ty
 */
export const useCompanyAuth = () => {
  /**
   * Đăng ký tài khoản công ty mới
   */
  const registerCompany = useMutation({
    mutationFn: (data: CompanyRegisterRequest) => {
      return CompanyAuthService.register(data);
    },
  });

  /**
   * <PERSON><PERSON><PERSON> thực email đăng ký
   */
  const verifyCompanyEmail = useMutation({
    mutationFn: (data: CompanyVerifyEmailRequest) => {
      return CompanyAuthService.verifyEmail(data);
    },
  });

  /**
   * Đăng nhập tài khoản công ty
   */
  const loginCompany = useMutation({
    mutationFn: (data: CompanyLoginRequest) => {
      return CompanyAuthService.login(data);
    },
  });

  return {
    registerCompany,
    verifyCompanyEmail,
    loginCompany,
  };
};
