import { Injectable, Logger } from '@nestjs/common';

/**
 * Mock implementation of RedisService for development and testing
 */
@Injectable()
export class MockRedisService {
  private readonly logger = new Logger(MockRedisService.name);
  
  async exists(key: string): Promise<boolean> {
    this.logger.log(`Mock RedisService.exists called with key: ${key}`);
    return false;
  }
  
  async get(key: string): Promise<string | null> {
    this.logger.log(`Mock RedisService.get called with key: ${key}`);
    return null;
  }
  
  async set(key: string, value: string | number | Buffer): Promise<'OK'> {
    this.logger.log(`Mock RedisService.set called with key: ${key}`);
    return 'OK';
  }
  
  async setWithExpiry(key: string, value: string | number | Buffer, expiryInSeconds: number): Promise<'OK'> {
    this.logger.log(`Mock RedisService.setWithExpiry called with key: ${key}, expiry: ${expiryInSeconds}`);
    return 'OK';
  }
  
  async del(keys: string | string[]): Promise<number> {
    this.logger.log(`Mock RedisService.del called with keys: ${JSON.stringify(keys)}`);
    return 1;
  }
}
