import { format, addDays, addWeeks, addMonths, addYears } from 'date-fns';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import {
  FormItem,
  Select,
  Input,
  DatePicker,
  Radio,
  Checkbox,
  Button,
  Icon,
  Typography,
} from '@/shared/components/common';

import {
  RecurrenceType,
  Weekday,
  RecurrenceEndType,
  RecurrenceOptions,
  RecurrenceConfig,
  RecurrenceEnd,
} from './recurrence.types';

/**
 * Props cho RecurrenceSelector
 */
export interface RecurrenceSelectorProps {
  /**
   * Giá trị hiện tại
   */
  value?: RecurrenceOptions | null;

  /**
   * Callback khi thay đổi giá trị
   */
  onChange?: (value: RecurrenceOptions | null) => void;

  /**
   * Ng<PERSON>y bắt đầu sự kiện
   */
  eventStartDate: Date;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * CSS class bổ sung
   */
  className?: string;
}

/**
 * Component RecurrenceSelector - <PERSON><PERSON><PERSON> cấu hình lặp lại sự kiện
 *
 * @example
 * ```tsx
 * const [recurrence, setRecurrence] = useState<RecurrenceOptions | null>(null);
 *
 * <RecurrenceSelector
 *   value={recurrence}
 *   onChange={setRecurrence}
 *   eventStartDate={new Date()}
 * />
 * ```
 */
const RecurrenceSelector: React.FC<RecurrenceSelectorProps> = ({
  value,
  onChange,
  eventStartDate,
  label,
  disabled = false,
  className = '',
}) => {
  const { t } = useTranslation();

  // State cho loại lặp lại
  const [recurrenceType, setRecurrenceType] = useState<RecurrenceType | ''>('');

  // State cho cấu hình lặp lại
  const [interval, setInterval] = useState<number>(1);
  const [weekdays, setWeekdays] = useState<Weekday[]>([]);
  const [monthDay, setMonthDay] = useState<number>(eventStartDate.getDate());
  const [monthWeekday, setMonthWeekday] = useState<Weekday>(Weekday.MONDAY);
  const [monthWeekPos, setMonthWeekPos] = useState<number>(1);
  const [yearMonth, setYearMonth] = useState<number>(eventStartDate.getMonth() + 1);
  const [yearMonthDay, setYearMonthDay] = useState<number>(eventStartDate.getDate());
  const [yearWeekday, setYearWeekday] = useState<Weekday>(Weekday.MONDAY);
  const [yearWeekPos, setYearWeekPos] = useState<number>(1);

  // State cho cấu hình kết thúc
  const [endType, setEndType] = useState<RecurrenceEndType>(RecurrenceEndType.NEVER);
  const [endCount, setEndCount] = useState<number>(10);
  const [endDate, setEndDate] = useState<Date | null>(null);

  // Khởi tạo giá trị từ prop value
  useEffect(() => {
    if (!value) {
      setRecurrenceType('');
      setInterval(1);
      setWeekdays([]);
      setMonthDay(eventStartDate.getDate());
      setMonthWeekday(Weekday.MONDAY);
      setMonthWeekPos(1);
      setYearMonth(eventStartDate.getMonth() + 1);
      setYearMonthDay(eventStartDate.getDate());
      setYearWeekday(Weekday.MONDAY);
      setYearWeekPos(1);
      setEndType(RecurrenceEndType.NEVER);
      setEndCount(10);
      setEndDate(null);
      return;
    }

    const { config, end } = value;

    // Cập nhật loại lặp lại
    setRecurrenceType(config.type);

    // Cập nhật cấu hình lặp lại
    if (config.type !== RecurrenceType.CUSTOM) {
      setInterval(config.interval);
    }

    // Cập nhật cấu hình theo loại
    switch (config.type) {
      case RecurrenceType.WEEKLY:
        setWeekdays(config.byDay);
        break;
      case RecurrenceType.MONTHLY:
        if ('byMonthDay' in config) {
          setMonthDay(config.byMonthDay);
        } else if ('byDay' in config) {
          setMonthWeekday(config.byDay);
          setMonthWeekPos(config.bySetPos);
        }
        break;
      case RecurrenceType.YEARLY:
        if ('byMonthDay' in config) {
          setYearMonth(config.byMonth);
          setYearMonthDay(config.byMonthDay);
        } else if ('byDay' in config) {
          setYearMonth(config.byMonth);
          setYearWeekday(config.byDay);
          setYearWeekPos(config.bySetPos);
        }
        break;
    }

    // Cập nhật cấu hình kết thúc
    setEndType(end.type);
    if (end.type === RecurrenceEndType.AFTER && end.count) {
      setEndCount(end.count);
    } else if (end.type === RecurrenceEndType.ON_DATE && end.until) {
      setEndDate(end.until);
    }
  }, [value, eventStartDate]);

  // Cập nhật giá trị khi thay đổi các thành phần
  useEffect(() => {
    if (!recurrenceType || !onChange) {return;}

    // Tạo cấu hình lặp lại
    let config: RecurrenceConfig;

    switch (recurrenceType) {
      case RecurrenceType.DAILY:
        config = {
          type: RecurrenceType.DAILY,
          interval,
        };
        break;
      case RecurrenceType.WEEKLY:
        config = {
          type: RecurrenceType.WEEKLY,
          interval,
          byDay: weekdays.length > 0 ? weekdays : [Weekday.MONDAY],
        };
        break;
      case RecurrenceType.MONTHLY:
        // Mặc định sử dụng byMonthDay
        config = {
          type: RecurrenceType.MONTHLY,
          interval,
          byMonthDay: monthDay,
        };
        break;
      case RecurrenceType.YEARLY:
        // Mặc định sử dụng byMonthDay
        config = {
          type: RecurrenceType.YEARLY,
          interval,
          byMonth: yearMonth,
          byMonthDay: yearMonthDay,
        };
        break;
      case RecurrenceType.CUSTOM:
        config = {
          type: RecurrenceType.CUSTOM,
          rrule: '',
        };
        break;
      default:
        return;
    }

    // Tạo cấu hình kết thúc
    const end: RecurrenceEnd = {
      type: endType,
    };

    if (endType === RecurrenceEndType.AFTER) {
      end.count = endCount;
    } else if (endType === RecurrenceEndType.ON_DATE && endDate) {
      end.until = endDate;
    }

    // Tạo cấu hình lặp lại đầy đủ
    const recurrenceOptions: RecurrenceOptions = {
      config,
      end,
      startDate: eventStartDate,
    };

    onChange(recurrenceOptions);
  }, [
    recurrenceType,
    interval,
    weekdays,
    monthDay,
    monthWeekday,
    monthWeekPos,
    yearMonth,
    yearMonthDay,
    yearWeekday,
    yearWeekPos,
    endType,
    endCount,
    endDate,
    onChange,
    eventStartDate,
  ]);

  // Xử lý khi thay đổi loại lặp lại
  const handleTypeChange = (value: string) => {
    if (value === '') {
      setRecurrenceType('');
      onChange?.(null);
      return;
    }

    setRecurrenceType(value as RecurrenceType);

    // Khởi tạo giá trị mặc định cho loại lặp lại
    switch (value as RecurrenceType) {
      case RecurrenceType.WEEKLY: {
        // Mặc định chọn ngày hiện tại trong tuần
        const currentDay = eventStartDate.getDay();
        const weekday = currentDay === 0 ? Weekday.SUNDAY : Object.values(Weekday)[currentDay - 1];
        setWeekdays([weekday]);
        break;
      }
    }
  };

  // Xử lý khi thay đổi các ngày trong tuần
  const handleWeekdayChange = (day: Weekday, checked: boolean) => {
    if (checked) {
      setWeekdays(prev => [...prev, day]);
    } else {
      setWeekdays(prev => prev.filter(d => d !== day));
    }
  };

  // Xử lý khi xóa cấu hình lặp lại
  const handleClearRecurrence = () => {
    setRecurrenceType('');
    onChange?.(null);
  };

  // Hiển thị mô tả lặp lại
  const getRecurrenceDescription = (): string => {
    if (!recurrenceType) {return t('calendar.noRecurrence', 'Không lặp lại');}

    let description = '';

    // Mô tả loại lặp lại
    switch (recurrenceType) {
      case RecurrenceType.DAILY:
        description =
          interval === 1
            ? t('calendar.everyDay', 'Hàng ngày')
            : t('calendar.everyXDays', 'Mỗi {{interval}} ngày', { interval });
        break;
      case RecurrenceType.WEEKLY:
        description =
          interval === 1
            ? t('calendar.everyWeekOn', 'Hàng tuần vào')
            : t('calendar.everyXWeeksOn', 'Mỗi {{interval}} tuần vào', { interval });

        // Thêm các ngày trong tuần
        if (weekdays.length > 0) {
          const dayNames = weekdays.map(day => {
            switch (day) {
              case Weekday.MONDAY:
                return t('calendar.monday', 'Thứ 2');
              case Weekday.TUESDAY:
                return t('calendar.tuesday', 'Thứ 3');
              case Weekday.WEDNESDAY:
                return t('calendar.wednesday', 'Thứ 4');
              case Weekday.THURSDAY:
                return t('calendar.thursday', 'Thứ 5');
              case Weekday.FRIDAY:
                return t('calendar.friday', 'Thứ 6');
              case Weekday.SATURDAY:
                return t('calendar.saturday', 'Thứ 7');
              case Weekday.SUNDAY:
                return t('calendar.sunday', 'Chủ nhật');
              default:
                return '';
            }
          });
          description += ` ${  dayNames.join(', ')}`;
        }
        break;
      case RecurrenceType.MONTHLY:
        description =
          interval === 1
            ? t('calendar.everyMonth', 'Hàng tháng')
            : t('calendar.everyXMonths', 'Mỗi {{interval}} tháng', { interval });

        description += ` ${t('calendar.onDay', 'vào ngày')} ${monthDay}`;
        break;
      case RecurrenceType.YEARLY: {
        description =
          interval === 1
            ? t('calendar.everyYear', 'Hàng năm')
            : t('calendar.everyXYears', 'Mỗi {{interval}} năm', { interval });

        const monthNames = [
          t('calendar.january', 'Tháng 1'),
          t('calendar.february', 'Tháng 2'),
          t('calendar.march', 'Tháng 3'),
          t('calendar.april', 'Tháng 4'),
          t('calendar.may', 'Tháng 5'),
          t('calendar.june', 'Tháng 6'),
          t('calendar.july', 'Tháng 7'),
          t('calendar.august', 'Tháng 8'),
          t('calendar.september', 'Tháng 9'),
          t('calendar.october', 'Tháng 10'),
          t('calendar.november', 'Tháng 11'),
          t('calendar.december', 'Tháng 12'),
        ];

        description += ` ${t('calendar.onDate', 'vào ngày')} ${yearMonthDay} ${monthNames[yearMonth - 1]}`;
        break;
      }
    }

    // Thêm mô tả kết thúc
    switch (endType) {
      case RecurrenceEndType.NEVER:
        description += `, ${t('calendar.noEndDate', 'không có ngày kết thúc')}`;
        break;
      case RecurrenceEndType.AFTER:
        description += `, ${t('calendar.endAfterXOccurrences', 'kết thúc sau {{count}} lần', { count: endCount })}`;
        break;
      case RecurrenceEndType.ON_DATE:
        if (endDate) {
          description += `, ${t('calendar.endOnDate', 'kết thúc vào ngày {{date}}', { date: format(endDate, 'dd/MM/yyyy') })}`;
        }
        break;
    }

    return description;
  };

  // Hiển thị các ngày lặp lại sắp tới
  const getUpcomingDates = (): Date[] => {
    if (!recurrenceType) {return [];}

    const dates: Date[] = [];
    let currentDate = new Date(eventStartDate);

    // Thêm 5 ngày lặp lại sắp tới
    for (let i = 0; i < 5; i++) {
      switch (recurrenceType) {
        case RecurrenceType.DAILY: {
          currentDate = addDays(currentDate, interval);
          break;
        }
        case RecurrenceType.WEEKLY: {
          currentDate = addWeeks(currentDate, interval);
          break;
        }
        case RecurrenceType.MONTHLY: {
          currentDate = addMonths(currentDate, interval);
          break;
        }
        case RecurrenceType.YEARLY: {
          currentDate = addYears(currentDate, interval);
          break;
        }
        default:
          return dates;
      }

      dates.push(new Date(currentDate));
    }

    return dates;
  };

  return (
    <div className={`recurrence-selector ${className}`}>
      {/* Label */}
      {label && (
        <Typography variant="body2" className="mb-2 font-medium">
          {label}
        </Typography>
      )}

      {/* Loại lặp lại */}
      <FormItem>
        <Select
          value={recurrenceType}
          onChange={val => handleTypeChange(val as string)}
          options={[
            { value: '', label: t('calendar.noRepeat', 'Không lặp lại') },
            { value: RecurrenceType.DAILY, label: t('calendar.daily', 'Hàng ngày') },
            { value: RecurrenceType.WEEKLY, label: t('calendar.weekly', 'Hàng tuần') },
            { value: RecurrenceType.MONTHLY, label: t('calendar.monthly', 'Hàng tháng') },
            { value: RecurrenceType.YEARLY, label: t('calendar.yearly', 'Hàng năm') },
          ]}
          placeholder={t('calendar.selectRecurrenceType', 'Chọn loại lặp lại')}
          disabled={disabled}
          fullWidth
        />
      </FormItem>

      {/* Cấu hình lặp lại */}
      {recurrenceType && (
        <div className="mt-4 p-4 bg-card-muted rounded-lg">
          {/* Mô tả lặp lại */}
          <div className="mb-4">
            <Typography variant="body2" className="font-medium">
              {getRecurrenceDescription()}
            </Typography>
          </div>

          {/* Cấu hình chi tiết */}
          <div className="space-y-4">
            {/* Cấu hình lặp lại hàng ngày */}
            {recurrenceType === RecurrenceType.DAILY && (
              <div className="flex items-center gap-2">
                <span>{t('calendar.every', 'Mỗi')}</span>
                <Input
                  type="number"
                  min={1}
                  max={365}
                  value={interval.toString()}
                  onChange={e => setInterval(parseInt(e.target.value) || 1)}
                  className="w-16"
                  disabled={disabled}
                />
                <span>{t('calendar.days', 'ngày')}</span>
              </div>
            )}

            {/* Cấu hình lặp lại hàng tuần */}
            {recurrenceType === RecurrenceType.WEEKLY && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span>{t('calendar.every', 'Mỗi')}</span>
                  <Input
                    type="number"
                    min={1}
                    max={52}
                    value={interval.toString()}
                    onChange={e => setInterval(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled}
                  />
                  <span>{t('calendar.weeks', 'tuần')}</span>
                </div>

                <div className="mt-2">
                  <Typography variant="body2" className="mb-1">
                    {t('calendar.onTheseWeekdays', 'Vào những ngày:')}
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    {Object.values(Weekday).map(day => (
                      <Checkbox
                        key={day}
                        checked={weekdays.includes(day)}
                        onChange={checked => handleWeekdayChange(day, checked)}
                        label={(() => {
                          switch (day) {
                            case Weekday.MONDAY:
                              return t('calendar.mondayShort', 'T2');
                            case Weekday.TUESDAY:
                              return t('calendar.tuesdayShort', 'T3');
                            case Weekday.WEDNESDAY:
                              return t('calendar.wednesdayShort', 'T4');
                            case Weekday.THURSDAY:
                              return t('calendar.thursdayShort', 'T5');
                            case Weekday.FRIDAY:
                              return t('calendar.fridayShort', 'T6');
                            case Weekday.SATURDAY:
                              return t('calendar.saturdayShort', 'T7');
                            case Weekday.SUNDAY:
                              return t('calendar.sundayShort', 'CN');
                          }
                        })()}
                        disabled={disabled}
                      />
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Cấu hình lặp lại hàng tháng */}
            {recurrenceType === RecurrenceType.MONTHLY && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span>{t('calendar.every', 'Mỗi')}</span>
                  <Input
                    type="number"
                    min={1}
                    max={12}
                    value={interval.toString()}
                    onChange={e => setInterval(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled}
                  />
                  <span>{t('calendar.months', 'tháng')}</span>
                </div>

                <div className="mt-2">
                  <Typography variant="body2" className="mb-1">
                    {t('calendar.onDay', 'Vào ngày:')}
                  </Typography>
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    value={monthDay.toString()}
                    onChange={e => setMonthDay(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled}
                  />
                </div>
              </div>
            )}

            {/* Cấu hình lặp lại hàng năm */}
            {recurrenceType === RecurrenceType.YEARLY && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span>{t('calendar.every', 'Mỗi')}</span>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    value={interval.toString()}
                    onChange={e => setInterval(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled}
                  />
                  <span>{t('calendar.years', 'năm')}</span>
                </div>

                <div className="mt-2 flex items-center gap-2">
                  <Typography variant="body2">{t('calendar.onDate', 'Vào ngày:')}</Typography>
                  <Input
                    type="number"
                    min={1}
                    max={31}
                    value={yearMonthDay.toString()}
                    onChange={e => setYearMonthDay(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled}
                  />
                  <Select
                    value={yearMonth.toString()}
                    onChange={val => setYearMonth(parseInt(val as string))}
                    options={[
                      { value: '1', label: t('calendar.january', 'Tháng 1') },
                      { value: '2', label: t('calendar.february', 'Tháng 2') },
                      { value: '3', label: t('calendar.march', 'Tháng 3') },
                      { value: '4', label: t('calendar.april', 'Tháng 4') },
                      { value: '5', label: t('calendar.may', 'Tháng 5') },
                      { value: '6', label: t('calendar.june', 'Tháng 6') },
                      { value: '7', label: t('calendar.july', 'Tháng 7') },
                      { value: '8', label: t('calendar.august', 'Tháng 8') },
                      { value: '9', label: t('calendar.september', 'Tháng 9') },
                      { value: '10', label: t('calendar.october', 'Tháng 10') },
                      { value: '11', label: t('calendar.november', 'Tháng 11') },
                      { value: '12', label: t('calendar.december', 'Tháng 12') },
                    ]}
                    disabled={disabled}
                  />
                </div>
              </div>
            )}

            {/* Cấu hình kết thúc */}
            <div className="mt-4">
              <Typography variant="body2" className="mb-2">
                {t('calendar.ends', 'Kết thúc:')}
              </Typography>

              <div className="space-y-2">
                <Radio
                  checked={endType === RecurrenceEndType.NEVER}
                  onChange={() => setEndType(RecurrenceEndType.NEVER)}
                  label={t('calendar.never', 'Không bao giờ')}
                  disabled={disabled}
                />

                <div className="flex items-center gap-2">
                  <Radio
                    checked={endType === RecurrenceEndType.AFTER}
                    onChange={() => setEndType(RecurrenceEndType.AFTER)}
                    disabled={disabled}
                  />
                  <span>{t('calendar.after', 'Sau')}</span>
                  <Input
                    type="number"
                    min={1}
                    max={999}
                    value={endCount.toString()}
                    onChange={e => setEndCount(parseInt(e.target.value) || 1)}
                    className="w-16"
                    disabled={disabled || endType !== RecurrenceEndType.AFTER}
                  />
                  <span>{t('calendar.occurrences', 'lần')}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Radio
                    checked={endType === RecurrenceEndType.ON_DATE}
                    onChange={() => setEndType(RecurrenceEndType.ON_DATE)}
                    disabled={disabled}
                  />
                  <span>{t('calendar.onDate', 'Vào ngày')}</span>
                  <DatePicker
                    value={endDate}
                    onChange={setEndDate}
                    format="dd/MM/yyyy"
                    minDate={eventStartDate}
                    disabled={disabled || endType !== RecurrenceEndType.ON_DATE}
                  />
                </div>
              </div>
            </div>

            {/* Xem trước các ngày lặp lại */}
            <div className="mt-4">
              <Typography variant="body2" className="mb-2">
                {t('calendar.upcomingDates', 'Các ngày sắp tới:')}
              </Typography>

              <div className="space-y-1">
                {getUpcomingDates().map((date, index) => (
                  <div key={index} className="text-sm">
                    {format(date, 'EEEE, dd/MM/yyyy', { locale: undefined })}
                  </div>
                ))}
              </div>
            </div>

            {/* Nút xóa cấu hình lặp lại */}
            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearRecurrence}
                disabled={disabled}
                leftIcon={<Icon name="trash" size="sm" />}
              >
                {t('calendar.removeRecurrence', 'Xóa lặp lại')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RecurrenceSelector;
