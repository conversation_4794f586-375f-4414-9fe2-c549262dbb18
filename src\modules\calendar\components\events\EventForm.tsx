import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Input,
  Textarea,
  DatePicker,
  Icon,
  Button,
  Modal,
  FormGrid,
  Toggle,
  FormItem,
} from '@/shared/components/common';

import { CalendarEvent } from '../../types';

export interface EventFormProps {
  /**
   * Sự kiện đang được chỉnh sửa hoặc tạo mới
   */
  event: CalendarEvent;

  /**
   * <PERSON>h sách các loại sự kiện
   */
  eventTypes: Array<{
    value: string;
    label: string;
    color: string;
  }>;

  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Tiêu đề của modal
   */
  title: string;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi lưu sự kiện
   */
  onSave: () => void;

  /**
   * Callback khi cập nhật trường của sự kiện
   */
  onUpdateField: (field: keyof CalendarEvent, value: string | Date | boolean | undefined) => void;

  /**
   * Callback khi cập nhật loại sự kiện
   */
  onUpdateEventType: (
    type: 'meeting' | 'appointment' | 'deadline' | 'lunch' | 'workshop' | 'planning'
  ) => void;
}

/**
 * Component form thêm/sửa sự kiện
 */
const EventForm: React.FC<EventFormProps> = ({
  event,
  eventTypes,
  isOpen,
  title,
  onClose,
  onSave,
  onUpdateField,
  onUpdateEventType,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="md"
      closeOnEsc={true}
      footer={
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            size="sm"
            className="px-3 py-1 rounded-md hover:bg-card-hover transition-colors"
          >
            <span className="text-sm">{t('common.cancel', 'Hủy')}</span>
          </Button>
          <Button
            variant="primary"
            disabled={!event.title.trim()}
            onClick={onSave}
            leftIcon={<Icon name="save" size="xs" />}
            size="sm"
            className="px-3 py-1 rounded-md shadow-sm hover:shadow transition-all"
          >
            <span className="text-sm">{t('common.save', 'Lưu')}</span>
          </Button>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Tiêu đề sự kiện */}
        <FormItem label={t('calendar.eventTitle', 'Tiêu đề')} required>
          <Input
            id="event-title"
            value={event.title}
            onChange={e => onUpdateField('title', e.target.value)}
            placeholder={t('calendar.enterTitle', 'Nhập tiêu đề sự kiện')}
            fullWidth
            autoFocus
          />
        </FormItem>

        {/* Loại sự kiện */}
        <FormItem label={t('calendar.eventType', 'Loại sự kiện')}>
          <div className="grid grid-cols-3 sm:grid-cols-6 gap-1">
            {eventTypes.map(type => {
              const isSelected = event.extendedProps?.type === type.value;
              const colorClass = type.color.includes('-')
                ? type.color // Nếu đã có định dạng như 'blue-500'
                : `${type.color}-500`; // Nếu chỉ có 'blue'

              return (
                <div
                  key={type.value}
                  onClick={() =>
                    onUpdateEventType(
                      type.value as
                        | 'meeting'
                        | 'appointment'
                        | 'deadline'
                        | 'lunch'
                        | 'workshop'
                        | 'planning'
                    )
                  }
                  className={`
                    cursor-pointer rounded-lg p-1 flex items-center justify-center transition-all
                    ${
                      isSelected
                        ? `bg-${colorClass}/10 border border-${colorClass} shadow-sm`
                        : 'bg-card hover:bg-card-hover border border-border'
                    }
                  `}
                  title={type.label}
                >
                  <div className={`w-3 h-3 rounded-full bg-${colorClass}`}></div>
                </div>
              );
            })}
          </div>
        </FormItem>

        {/* Thời gian */}
        <div className="bg-card border border-border rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium">{t('calendar.dateAndTime', 'Thời gian')}</label>
            <div className="flex items-center gap-2">
              <span className="text-xs">{t('calendar.allDay', 'Cả ngày')}</span>
              <Toggle
                checked={event.allDay || false}
                onChange={checked => onUpdateField('allDay', checked)}
                size="sm"
              />
            </div>
          </div>

          <FormGrid columns={2} columnsSm={1} gap="md" className="mt-2">
            <div>
              <label className="block text-xs mb-1">{t('calendar.startDate', 'Bắt đầu')}</label>
              <DatePicker
                value={typeof event.start === 'string' ? new Date(event.start) : event.start}
                onChange={date => onUpdateField('start', date || new Date())}
                format={event.allDay ? 'dd/MM/yyyy' : 'dd/MM/yyyy HH:mm'}
                showCalendarIcon={true}
                clearable={false}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-xs mb-1">{t('calendar.endDate', 'Kết thúc')}</label>
              <DatePicker
                value={
                  event.end
                    ? typeof event.end === 'string'
                      ? new Date(event.end)
                      : event.end
                    : undefined
                }
                onChange={date => onUpdateField('end', date || undefined)}
                format={event.allDay ? 'dd/MM/yyyy' : 'dd/MM/yyyy HH:mm'}
                showCalendarIcon={true}
                clearable={false}
                minDate={typeof event.start === 'string' ? new Date(event.start) : event.start}
                className="w-full"
              />
            </div>
          </FormGrid>
        </div>

        {/* Địa điểm và Mô tả */}
        <FormGrid columns={2} columnsSm={1} gap="md">
          <FormItem label={t('calendar.location', 'Địa điểm')}>
            <Input
              id="event-location"
              value={event.location || ''}
              onChange={e => onUpdateField('location', e.target.value)}
              placeholder={t('calendar.enterLocation', 'Nhập địa điểm')}
              fullWidth
              leftIcon={<Icon name="map-pin" size="sm" />}
            />
          </FormItem>

          <FormItem label={t('calendar.description', 'Mô tả')}>
            <Textarea
              id="event-description"
              value={event.description || ''}
              onChange={e => onUpdateField('description', e.target.value)}
              placeholder={t('calendar.enterDescription', 'Nhập mô tả')}
              rows={1}
              fullWidth
              className="resize-none"
            />
          </FormItem>
        </FormGrid>
      </div>
    </Modal>
  );
};

export default EventForm;
