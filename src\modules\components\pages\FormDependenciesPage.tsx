import React, { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Form, FormItem, Input, Button, FormGrid, Toggle } from '@/shared/components/common';

import { ComponentDemo } from '../components';

// Sample data for cascading select
const countries = [
  { value: 'vietnam', label: 'Vietnam' },
  { value: 'usa', label: 'United States' },
  { value: 'japan', label: 'Japan' },
];

const provinces = {
  vietnam: [
    { value: 'hanoi', label: 'Hanoi' },
    { value: 'hochiminh', label: 'Ho Chi Minh City' },
    { value: 'danang', label: 'Da Nang' },
  ],
  usa: [
    { value: 'california', label: 'California' },
    { value: 'newyork', label: 'New York' },
    { value: 'texas', label: 'Texas' },
  ],
  japan: [
    { value: 'tokyo', label: 'Tokyo' },
    { value: 'osaka', label: 'Osaka' },
    { value: 'kyoto', label: 'Kyoto' },
  ],
};

// Transform Dependencies Handler
const TransformDependencyHandler: React.FC = () => {
  const { watch, setValue } = useFormContext();

  // Watch fields
  const firstName = watch('firstName');
  const lastName = watch('lastName');
  const age = watch('age');

  // Update fullName when firstName or lastName changes
  useEffect(() => {
    const fullName = `${firstName || ''} ${lastName || ''}`.trim();
    setValue('fullName', fullName);
  }, [firstName, lastName, setValue]);

  // Update isAdult when age changes
  useEffect(() => {
    const ageNum = parseInt(age as string, 10);
    const isAdult = !isNaN(ageNum) && ageNum >= 18;
    setValue('isAdult', isAdult);
  }, [age, setValue]);

  return null;
};

// Cascading Select Handler
const CascadingSelectHandler: React.FC = () => {
  const { watch, setValue } = useFormContext();
  const country = watch('country');

  // Update provinces when country changes
  useEffect(() => {
    // Reset province when country changes
    setValue('province', '');
  }, [country, setValue]);

  return null;
};

// Province Select Component
const ProvinceSelect: React.FC = () => {
  const { watch } = useFormContext();
  const country = watch('country');
  const options = provinces[country as keyof typeof provinces] || [];

  return (
    <select className="w-full p-2 border rounded">
      <option value="">Select province/state</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Payment Method Handler
const PaymentMethodHandler: React.FC = () => {
  const { watch, setValue } = useFormContext();
  const paymentMethod = watch('paymentMethod');

  // Reset fields when payment method changes
  useEffect(() => {
    if (paymentMethod === 'credit_card') {
      setValue('bankName', '');
      setValue('accountNumber', '');
    } else if (paymentMethod === 'bank_transfer') {
      setValue('cardNumber', '');
      setValue('cardExpiry', '');
      setValue('cardCvv', '');
    }
  }, [paymentMethod, setValue]);

  return null;
};

// Payment Fields Renderer
const PaymentFieldsRenderer: React.FC = () => {
  const { watch } = useFormContext();
  const paymentMethod = watch('paymentMethod');

  if (paymentMethod === 'credit_card') {
    return (
      <FormGrid columns={2} gap="md">
        <FormItem name="cardNumber" label="Card Number" required>
          <Input placeholder="XXXX XXXX XXXX XXXX" fullWidth />
        </FormItem>

        <FormItem name="cardExpiry" label="Expiry Date" required>
          <Input placeholder="MM/YY" fullWidth />
        </FormItem>

        <FormItem name="cardCvv" label="CVV" required>
          <Input placeholder="XXX" fullWidth />
        </FormItem>
      </FormGrid>
    );
  }

  if (paymentMethod === 'bank_transfer') {
    return (
      <FormGrid columns={2} gap="md">
        <FormItem name="bankName" label="Bank Name" required>
          <Input placeholder="Enter bank name" fullWidth />
        </FormItem>

        <FormItem name="accountNumber" label="Account Number" required>
          <Input placeholder="Enter account number" fullWidth />
        </FormItem>
      </FormGrid>
    );
  }

  return null;
};

const FormDependenciesPage: React.FC = () => {
  const { t } = useTranslation();

  // Transform Dependencies Schema
  const transformSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    fullName: z.string().optional(),
    age: z.string().optional(),
    isAdult: z.boolean().optional(),
  });

  // Cascading Select Schema
  const cascadingSchema = z.object({
    country: z.string().min(1, 'Country is required'),
    province: z.string().min(1, 'Province/State is required'),
  });

  // Payment Method Schema
  const paymentSchema = z.object({
    paymentMethod: z.enum(['credit_card', 'bank_transfer']),
    cardNumber: z.string().optional(),
    cardExpiry: z.string().optional(),
    cardCvv: z.string().optional(),
    bankName: z.string().optional(),
    accountNumber: z.string().optional(),
  });

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.formDependencies.title', 'Form Field Dependencies')}
        </h1>
        <p className="text-muted">
          {t(
            'components.formDependencies.description',
            'Manage dependencies between form fields to create dynamic forms.'
          )}
        </p>
      </div>

      {/* Transform Dependencies */}
      <ComponentDemo
        title={t('components.formDependencies.transform.title', 'Transform Dependencies')}
        description={t(
          'components.formDependencies.transform.description',
          'Update field values based on other fields using transform functions.'
        )}
        code={`import { useFormContext } from 'react-hook-form';
import { useEffect } from 'react';

// Inside your form component
const TransformDependencyHandler = () => {
  const { watch, setValue } = useFormContext();

  // Watch fields
  const firstName = watch('firstName');
  const lastName = watch('lastName');
  const age = watch('age');

  // Update fullName when firstName or lastName changes
  useEffect(() => {
    const fullName = \`\${firstName || ''} \${lastName || ''}\`.trim();
    setValue('fullName', fullName);
  }, [firstName, lastName, setValue]);

  // Update isAdult when age changes
  useEffect(() => {
    const ageNum = parseInt(age, 10);
    const isAdult = !isNaN(ageNum) && ageNum >= 18;
    setValue('isAdult', isAdult);
  }, [age, setValue]);

  return null;
};

// Use in your form
<Form schema={schema} onSubmit={handleSubmit}>
  <TransformDependencyHandler />

  <FormItem name="firstName" label="First Name" required>
    <Input />
  </FormItem>

  <FormItem name="lastName" label="Last Name" required>
    <Input />
  </FormItem>

  <FormItem name="fullName" label="Full Name" disabled>
    <Input />
  </FormItem>

  <FormItem name="age" label="Age">
    <Input type="number" />
  </FormItem>

  <FormItem name="isAdult" label="Is Adult" inline>
    <Toggle disabled />
  </FormItem>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={transformSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <TransformDependencyHandler />

            <FormGrid columns={2} gap="md">
              <FormItem name="firstName" label="First Name" required>
                <Input placeholder="Enter first name" fullWidth />
              </FormItem>

              <FormItem name="lastName" label="Last Name" required>
                <Input placeholder="Enter last name" fullWidth />
              </FormItem>
            </FormGrid>

            <FormItem name="fullName" label="Full Name">
              <Input placeholder="Auto-generated from first and last name" fullWidth disabled />
            </FormItem>

            <FormGrid columns={2} gap="md">
              <FormItem name="age" label="Age">
                <Input type="number" placeholder="Enter age" fullWidth />
              </FormItem>

              <FormItem name="isAdult" label="Is Adult" inline>
                <Toggle disabled />
              </FormItem>
            </FormGrid>

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Cascading Select */}
      <ComponentDemo
        title={t('components.formDependencies.cascadingSelect.title', 'Cascading Select')}
        description={t(
          'components.formDependencies.cascadingSelect.description',
          'Update select options based on the value of another select.'
        )}
        code={`import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';

// Province Select Component
const ProvinceSelect = () => {
  const { watch } = useFormContext();
  const country = watch('country');
  const options = getProvincesByCountry(country);

  return (
    <select>
      <option value="">Select province/state</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Cascading Select Handler
const CascadingSelectHandler = () => {
  const { watch, setValue } = useFormContext();
  const country = watch('country');

  // Reset province when country changes
  useEffect(() => {
    setValue('province', '');
  }, [country, setValue]);

  return null;
};

// Use in your form
<Form schema={schema} onSubmit={handleSubmit}>
  <CascadingSelectHandler />

  <FormItem name="country" label="Country" required>
    <select>
      <option value="">Select country</option>
      {countries.map(country => (
        <option key={country.value} value={country.value}>
          {country.label}
        </option>
      ))}
    </select>
  </FormItem>

  <FormItem name="province" label="Province/State" required>
    <ProvinceSelect />
  </FormItem>
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={cascadingSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
          >
            <CascadingSelectHandler />

            <FormItem name="country" label="Country" required>
              <select className="w-full p-2 border rounded">
                <option value="">Select country</option>
                {countries.map(country => (
                  <option key={country.value} value={country.value}>
                    {country.label}
                  </option>
                ))}
              </select>
            </FormItem>

            <FormItem name="province" label="Province/State" required>
              <ProvinceSelect />
            </FormItem>

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>

      {/* Reset Fields */}
      <ComponentDemo
        title={t('components.formDependencies.resetFields.title', 'Reset Fields')}
        description={t(
          'components.formDependencies.resetFields.description',
          'Reset field values when another field changes.'
        )}
        code={`import { useFormContext } from 'react-hook-form';
import { useEffect } from 'react';

// Payment Method Handler
const PaymentMethodHandler = () => {
  const { watch, setValue } = useFormContext();
  const paymentMethod = watch('paymentMethod');

  // Reset fields when payment method changes
  useEffect(() => {
    if (paymentMethod === 'credit_card') {
      setValue('bankName', '');
      setValue('accountNumber', '');
    } else if (paymentMethod === 'bank_transfer') {
      setValue('cardNumber', '');
      setValue('cardExpiry', '');
      setValue('cardCvv', '');
    }
  }, [paymentMethod, setValue]);

  return null;
};

// Use in your form
<Form schema={schema} onSubmit={handleSubmit}>
  <PaymentMethodHandler />

  <FormItem name="paymentMethod" label="Payment Method" required>
    <select>
      <option value="credit_card">Credit Card</option>
      <option value="bank_transfer">Bank Transfer</option>
    </select>
  </FormItem>

  {/* Credit Card Fields */}
  {paymentMethod === 'credit_card' && (
    <>
      <FormItem name="cardNumber" label="Card Number" required>
        <Input />
      </FormItem>
      {/* Other credit card fields */}
    </>
  )}

  {/* Bank Transfer Fields */}
  {paymentMethod === 'bank_transfer' && (
    <>
      <FormItem name="bankName" label="Bank Name" required>
        <Input />
      </FormItem>
      {/* Other bank transfer fields */}
    </>
  )}
</Form>`}
      >
        <div className="w-full max-w-md mx-auto">
          <Form
            schema={paymentSchema}
            onSubmit={values => console.log(values)}
            className="space-y-4"
            defaultValues={{
              paymentMethod: 'credit_card',
            }}
          >
            <PaymentMethodHandler />

            <FormItem name="paymentMethod" label="Payment Method" required>
              <select className="w-full p-2 border rounded">
                <option value="credit_card">Credit Card</option>
                <option value="bank_transfer">Bank Transfer</option>
              </select>
            </FormItem>

            {/* Payment Fields will be rendered by PaymentFieldsRenderer */}
            <PaymentFieldsRenderer />

            <Button type="submit">Submit</Button>
          </Form>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default FormDependenciesPage;
