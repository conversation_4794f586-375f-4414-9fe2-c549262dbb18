import React from 'react';

import { cn } from '@/shared/utils/cn';

export interface EventBadgeProps {
  /**
   * Loại sự kiện
   */
  type: 'meeting' | 'appointment' | 'deadline' | 'lunch' | 'workshop' | 'planning' | string;

  /**
   * <PERSON><PERSON>ch thước badge
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Màu tùy chỉnh (nếu không sử dụng loại mặc định)
   */
  color?: string;

  /**
   * CSS class bổ sung
   */
  className?: string;

  /**
   * Hiển thị nhãn
   */
  showLabel?: boolean;

  /**
   * Nhãn tùy chỉnh
   */
  label?: string;
}

/**
 * Component hiển thị badge cho loại sự kiện
 *
 * @example
 * ```tsx
 * <EventBadge type="meeting" />
 * <EventBadge type="deadline" size="lg" showLabel />
 * <EventBadge type="custom" color="blue-500" label="Custom Event" showLabel />
 * ```
 */
const EventBadge: React.FC<EventBadgeProps> = ({
  type,
  size = 'md',
  color,
  className,
  showLabel = false,
  label,
}) => {
  // Map loại sự kiện sang màu sắc
  const getColorClass = () => {
    if (color) {
      return color.includes('-') ? color : `${color}-500`;
    }

    switch (type) {
      case 'meeting':
        return 'primary';
      case 'appointment':
        return 'blue-500';
      case 'deadline':
        return 'red-500';
      case 'lunch':
        return 'green-500';
      case 'workshop':
        return 'yellow-500';
      case 'planning':
        return 'purple-500';
      default:
        return 'gray-500';
    }
  };

  // Map loại sự kiện sang nhãn
  const getLabel = () => {
    if (label) {return label;}

    switch (type) {
      case 'meeting':
        return 'Cuộc họp';
      case 'appointment':
        return 'Cuộc hẹn';
      case 'deadline':
        return 'Hạn chót';
      case 'lunch':
        return 'Ăn trưa';
      case 'workshop':
        return 'Hội thảo';
      case 'planning':
        return 'Lập kế hoạch';
      default:
        return type;
    }
  };

  // Kích thước badge
  const sizeClass = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  }[size];

  const colorClass = getColorClass();

  return (
    <div className={cn('flex items-center gap-1.5', className)}>
      <div className={cn(`rounded-full bg-${colorClass}`, sizeClass)}></div>
      {showLabel && <span className="text-xs font-medium">{getLabel()}</span>}
    </div>
  );
};

export default EventBadge;
