import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

export const AUTH_ERROR_CODE = {
  // ===== AUTHENTICATION ERRORS (10001-10099) =====
  INVALID_CREDENTIALS: new ErrorCode(
    10001,
    'Email hoặc mật khẩu không chính xác',
    HttpStatus.UNAUTHORIZED,
  ),

  ACCOUNT_LOCKED: new ErrorCode(
    10002,
    'Tài khoản đã bị khóa',
    HttpStatus.FORBIDDEN,
  ),

  ACCOUNT_NOT_VERIFIED: new ErrorCode(
    10003,
    'Tài khoản chưa được xác thực',
    HttpStatus.FORBIDDEN,
  ),

  INVALID_TOKEN: new ErrorCode(
    10004,
    'Token không hợp lệ hoặc đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  TOKEN_EXPIRED: new ErrorCode(
    10005,
    'Token đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  REFRESH_TOKEN_INVALID: new ErrorCode(
    10006,
    'Refresh token không hợp lệ hoặc đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  // ===== SOCIAL AUTHENTICATION ERRORS (10040-10059) =====
  SOCIAL_AUTH_FAILED: new ErrorCode(
    10040,
    'Xác thực mạng xã hội thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  INVALID_SOCIAL_PROVIDER: new ErrorCode(
    10041,
    'Nhà cung cấp mạng xã hội không được hỗ trợ',
    HttpStatus.BAD_REQUEST,
  ),

  INVALID_SOCIAL_TOKEN: new ErrorCode(
    10042,
    'Token mạng xã hội không hợp lệ hoặc đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  SOCIAL_ACCOUNT_NOT_FOUND: new ErrorCode(
    10043,
    'Tài khoản mạng xã hội chưa được liên kết với hệ thống',
    HttpStatus.NOT_FOUND,
  ),

  SOCIAL_ACCOUNT_EXISTS: new ErrorCode(
    10044,
    'Tài khoản mạng xã hội đã được liên kết với hệ thống',
    HttpStatus.CONFLICT,
  ),

  SOCIAL_EMAIL_REQUIRED: new ErrorCode(
    10045,
    'Email là bắt buộc để đăng ký tài khoản',
    HttpStatus.BAD_REQUEST,
  ),

  SOCIAL_EMAIL_EXISTS: new ErrorCode(
    10046,
    'Email đã được sử dụng bởi tài khoản khác',
    HttpStatus.CONFLICT,
  ),

  USER_NOT_FOUND: new ErrorCode(
    10007,
    'Không tìm thấy người dùng',
    HttpStatus.NOT_FOUND,
  ),

  EMAIL_ALREADY_EXISTS: new ErrorCode(
    10008,
    'Email đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  USERNAME_ALREADY_EXISTS: new ErrorCode(
    10030,
    'Tên đăng nhập đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  PHONE_ALREADY_EXISTS: new ErrorCode(
    10009,
    'Số điện thoại đã được sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  VERIFICATION_CODE_INVALID: new ErrorCode(
    10010,
    'Mã xác thực không hợp lệ hoặc đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),

  PASSWORD_RESET_CODE_INVALID: new ErrorCode(
    10011,
    'Mã đặt lại mật khẩu không hợp lệ hoặc đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),

  PASSWORD_MISMATCH: new ErrorCode(
    10012,
    'Mật khẩu mới và xác nhận mật khẩu không khớp',
    HttpStatus.BAD_REQUEST,
  ),

  INVALID_CURRENT_PASSWORD: new ErrorCode(
    10027,
    'Mật khẩu hiện tại không chính xác',
    HttpStatus.BAD_REQUEST,
  ),

  PASSWORD_SAME_AS_OLD: new ErrorCode(
    10013,
    'Mật khẩu mới không được trùng với mật khẩu cũ',
    HttpStatus.BAD_REQUEST,
  ),

  SOCIAL_LOGIN_FAILED: new ErrorCode(
    10014,
    'Đăng nhập bằng mạng xã hội thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  EMAIL_SEND_FAILED: new ErrorCode(
    10015,
    'Không thể gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  SMS_SEND_FAILED: new ErrorCode(
    10016,
    'Không thể gửi SMS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOO_MANY_REQUESTS: new ErrorCode(
    10017,
    'Quá nhiều yêu cầu, vui lòng thử lại sau',
    HttpStatus.TOO_MANY_REQUESTS,
  ),

  UNAUTHORIZED_ACCESS: new ErrorCode(
    10018,
    'Không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  ),

  REGISTRATION_FAILED: new ErrorCode(
    10019,
    'Đăng ký tài khoản thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  LOGIN_FAILED: new ErrorCode(
    10020,
    'Đăng nhập thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  LOGOUT_FAILED: new ErrorCode(
    10021,
    'Đăng xuất thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VERIFICATION_FAILED: new ErrorCode(
    10022,
    'Xác thực tài khoản thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PASSWORD_RESET_FAILED: new ErrorCode(
    10023,
    'Đặt lại mật khẩu thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PASSWORD_CHANGE_FAILED: new ErrorCode(
    10024,
    'Thay đổi mật khẩu thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  EMPLOYEE_NOT_FOUND: new ErrorCode(
    10025,
    'Không tìm thấy nhân viên',
    HttpStatus.NOT_FOUND,
  ),

  EMPLOYEE_INACTIVE: new ErrorCode(
    10026,
    'Tài khoản nhân viên đã bị vô hiệu hóa',
    HttpStatus.FORBIDDEN,
  ),

  TOKEN_INVALID_OR_EXPIRED: new ErrorCode(
    10028,
    'Token không hợp lệ hoặc đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),

  EMAIL_SENDING_ERROR: new ErrorCode(
    10029,
    'Không thể gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  ACCOUNT_ALREADY_VERIFIED: new ErrorCode(
    10031,
    'Tài khoản đã được xác thực',
    HttpStatus.BAD_REQUEST,
  ),
};
