import { IsEmail, IsNotEmpty, IsString, Matches, MinLength } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho yêu cầu quên mật khẩu
 */
export class ForgotPasswordDto {
  @ApiProperty({
    description: 'Email của tài khoản cần đặt lại mật khẩu',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Email không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;
}

/**
 * DTO cho phản hồi yêu cầu quên mật khẩu thành công
 */
export class ForgotPasswordResponseDto {
  @ApiProperty({
    description: 'Thông báo gửi email đặt lại mật khẩu thành công',
    example: 'Email đặt lại mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.',
  })
  message: string;
}

/**
 * DTO cho yêu cầu đặt lại mật khẩu
 */
export class ResetPasswordDto {
  @ApiProperty({
    description: 'Token đặt lại mật khẩu',
    example: 'a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6',
  })
  @IsNotEmpty({ message: 'Token không được để trống' })
  @IsString({ message: 'Token phải là chuỗi' })
  token: string;

  @ApiProperty({
    description: 'Mật khẩu mới',
    example: 'NewStrongPassword456!',
  })
  @IsNotEmpty({ message: 'Mật khẩu mới không được để trống' })
  @IsString({ message: 'Mật khẩu mới phải là chuỗi' })
  @MinLength(8, { message: 'Mật khẩu mới phải có ít nhất 8 ký tự' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%&*?@])[\d!$%&*?@A-Za-z]+$/, {
    message:
      'Mật khẩu mới phải chứa ít nhất một chữ cái viết thường, một chữ cái viết hoa, một chữ số và một ký tự đặc biệt',
  })
  newPassword: string;

  @ApiProperty({
    description: 'Xác nhận mật khẩu mới',
    example: 'NewStrongPassword456!',
  })
  @IsNotEmpty({ message: 'Xác nhận mật khẩu không được để trống' })
  @IsString({ message: 'Xác nhận mật khẩu phải là chuỗi' })
  confirmPassword: string;
}

/**
 * DTO cho phản hồi đặt lại mật khẩu thành công
 */
export class ResetPasswordResponseDto {
  @ApiProperty({
    description: 'Thông báo đặt lại mật khẩu thành công',
    example: 'Đặt lại mật khẩu thành công. Bạn có thể đăng nhập bằng mật khẩu mới.',
  })
  message: string;
}
