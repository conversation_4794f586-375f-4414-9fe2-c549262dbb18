import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import useSlideForm from '@/shared/hooks/useSlideForm';

import PublisherForm from '../components/forms/PublisherForm';
import { PublisherDto, PublisherStatus } from '../types/affiliate.types';

/**
 * Trang quản lý danh sách publisher
 */
const PublisherListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Mock data cho Publisher
  const publisherData = useMemo<PublisherDto[]>(
    () => [
      {
        id: 1,
        userId: 101,
        userName: 'Nguyễn Văn A',
        userEmail: '<EMAIL>',
        userPhone: '0901234567',
        referralCode: 'NVANA2023',
        totalReferrals: 25,
        totalCommission: 2500000,
        status: PublisherStatus.ACTIVE,
        createdAt: '2023-07-15',
        updatedAt: '2023-07-15',
      },
      {
        id: 2,
        userId: 102,
        userName: 'Trần Thị B',
        userEmail: '<EMAIL>',
        userPhone: '0912345678',
        referralCode: 'TTHIB2023',
        totalReferrals: 18,
        totalCommission: 1800000,
        status: PublisherStatus.ACTIVE,
        createdAt: '2023-07-16',
        updatedAt: '2023-07-16',
      },
      {
        id: 3,
        userId: 103,
        userName: 'Lê Văn C',
        userEmail: '<EMAIL>',
        userPhone: '0923456789',
        referralCode: 'LVANC2023',
        totalReferrals: 10,
        totalCommission: 1000000,
        status: PublisherStatus.INACTIVE,
        createdAt: '2023-07-17',
        updatedAt: '2023-07-17',
      },
      {
        id: 4,
        userId: 104,
        userName: 'Phạm Thị D',
        userEmail: '<EMAIL>',
        userPhone: '0934567890',
        referralCode: 'PTHID2023',
        totalReferrals: 5,
        totalCommission: 500000,
        status: PublisherStatus.PENDING,
        createdAt: '2023-07-18',
        updatedAt: '2023-07-18',
      },
      {
        id: 5,
        userId: 105,
        userName: 'Hoàng Văn E',
        userEmail: '<EMAIL>',
        userPhone: '0945678901',
        referralCode: 'HVANE2023',
        totalReferrals: 0,
        totalCommission: 0,
        status: PublisherStatus.SUSPENDED,
        createdAt: '2023-07-19',
        updatedAt: '2023-07-19',
      },
    ],
    []
  );

  // Columns cho bảng
  const columns: TableColumn<PublisherDto>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'userName',
      title: t('affiliate:publisher.table.userName'),
      dataIndex: 'userName',
      width: '15%',
    },
    {
      key: 'userEmail',
      title: t('affiliate:publisher.table.userEmail'),
      dataIndex: 'userEmail',
      width: '15%',
    },
    {
      key: 'referralCode',
      title: t('affiliate:publisher.table.referralCode'),
      dataIndex: 'referralCode',
      width: '10%',
    },
    {
      key: 'totalReferrals',
      title: t('affiliate:publisher.table.totalReferrals'),
      dataIndex: 'totalReferrals',
      width: '10%',
    },
    {
      key: 'totalCommission',
      title: t('affiliate:publisher.table.totalCommission'),
      dataIndex: 'totalCommission',
      width: '10%',
      render: (value: unknown) => {
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN').format(value as number)} đ
          </div>
        );
      },
    },
    {
      key: 'status',
      title: t('affiliate:publisher.table.status'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => {
        const status = value as PublisherStatus;
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              status === PublisherStatus.ACTIVE
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : status === PublisherStatus.PENDING
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : status === PublisherStatus.SUSPENDED
                    ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
            }`}
          >
            {status === PublisherStatus.ACTIVE
              ? t('common:active')
              : status === PublisherStatus.PENDING
                ? t('common:pending')
                : status === PublisherStatus.SUSPENDED
                  ? t('common:suspended')
                  : t('common:inactive')}
          </div>
        );
      },
    },
    {
      key: 'createdAt',
      title: t('affiliate:publisher.table.createdAt'),
      dataIndex: 'createdAt',
      width: '10%',
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '15%',
      render: (_: unknown, record: PublisherDto) => (
        <div className="flex space-x-2">
          <Tooltip content={t('common:view')} position="top">
            <IconCard
              icon="eye"
              variant="default"
              size="sm"
              onClick={() => console.log('View', record.id)}
            />
          </Tooltip>
          <Tooltip content={t('common:edit')} position="top">
            <IconCard
              icon="edit"
              variant="default"
              size="sm"
              onClick={() => console.log('Edit', record.id)}
            />
          </Tooltip>
          <Tooltip content={t('common:delete')} position="top">
            <IconCard
              icon="trash"
              variant="default"
              size="sm"
              onClick={() => console.log('Delete', record.id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  // Lọc dữ liệu
  const filteredData = useMemo(() => {
    return publisherData.filter((publisher: PublisherDto) => {
      const matchesSearch =
        publisher.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        publisher.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        publisher.referralCode.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filter === 'all' || publisher.status === filter;
      return matchesSearch && matchesFilter;
    });
  }, [publisherData, searchTerm, filter]);

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form values:', values);
    // Xử lý thêm publisher
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'active',
            label: t('common:active'),
            icon: 'check',
            onClick: () => setFilter(PublisherStatus.ACTIVE),
          },
          {
            id: 'pending',
            label: t('common:pending'),
            icon: 'clock',
            onClick: () => setFilter(PublisherStatus.PENDING),
          },
          {
            id: 'inactive',
            label: t('common:inactive'),
            icon: 'eye-off',
            onClick: () => setFilter(PublisherStatus.INACTIVE),
          },
          {
            id: 'suspended',
            label: t('common:suspended'),
            icon: 'alert-triangle',
            onClick: () => setFilter(PublisherStatus.SUSPENDED),
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <PublisherForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table columns={columns} data={filteredData} rowKey="id" pagination />
      </Card>
    </div>
  );
};

export default PublisherListPage;
