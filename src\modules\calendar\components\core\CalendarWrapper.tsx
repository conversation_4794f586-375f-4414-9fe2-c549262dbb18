import React, { useState, useEffect } from 'react';

import { useTheme } from '@/shared/contexts/theme';
import { cn } from '@/shared/utils/cn';
import { DateSelectArg } from '@fullcalendar/core';
import viLocale from '@fullcalendar/core/locales/vi';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';

import useCalendar from '../../hooks/useCalendar';
import { CalendarProps, CalendarEvent } from '../../types';
import EventForm from '../events/EventForm';
import CalendarHeader from '../ui/CalendarHeader';
import CalendarToolbar from '../ui/CalendarToolbar';

/**
 * Component wrapper cho calendar với đầy đủ tính năng
 *
 * @example
 * ```tsx
 * <CalendarWrapper
 *   events={myEvents}
 *   initialView="dayGridMonth"
 *   editable={true}
 *   selectable={true}
 *   onEventClick={(info) => console.log('Event clicked:', info.event.title)}
 * />
 * ```
 */
const CalendarWrapper: React.FC<CalendarProps> = ({
  events = [],
  initialDate,
  initialView = 'dayGridMonth',
  weekends = true,
  editable = true,
  selectable = true,
  allDaySlot = false,
  height = 'auto',
  className = '',
  onDateSelect,
  // Các callback từ props (không sử dụng trực tiếp)
   
  onEventClick: _onEventClick,
   
  onEventChange: _onEventChange,
  onAddEvent,
  onUpdateEvent,
  onDeleteEvent,
}) => {
  // Sử dụng hook useCalendar để quản lý state và logic
  const {
    events: calendarEvents,
    currentView,
    calendarRef,
    calendarTitle,
    // Không sử dụng trong component này
     
    selectedEvent: _selectedEvent,
     
    setSelectedEvent: _setSelectedEvent,
    handleDateSelect: handleDateSelectInternal,
    handleEventClick: handleEventClickInternal,
    handleEventChange: handleEventChangeInternal,
    handleViewChange,
    handlePrev,
    handleNext,
    handleToday,
    addEvent,
    // Không sử dụng trong component này
     
    updateEvent: _updateEvent,
     
    deleteEvent: _deleteEvent,
  } = useCalendar({
    initialEvents: events,
    initialView,
    onAddEvent,
    onUpdateEvent,
    onDeleteEvent,
  });

  // State
  const [showWeekends, setShowWeekends] = useState<boolean>(weekends);
  const [showEventModal, setShowEventModal] = useState<boolean>(false);
  const [newEvent, setNewEvent] = useState<CalendarEvent | null>(null);

  // Theme
  const { themeMode } = useTheme();

  // Xử lý khi chọn ngày/khoảng thời gian
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    // Gọi callback nội bộ
    handleDateSelectInternal(selectInfo);

    // Tạo sự kiện mới
    const calendarApi = selectInfo.view.calendar;
    calendarApi.unselect();

    const start = selectInfo.start;
    const end = selectInfo.end;

    // Tạo sự kiện mới
    const event: CalendarEvent = {
      id: '',
      title: '',
      start,
      end,
      allDay: selectInfo.allDay,
      extendedProps: {
        type: 'meeting',
      },
    };

    setNewEvent(event);
    setShowEventModal(true);

    // Gọi callback từ props nếu có
    if (onDateSelect) {
      onDateSelect(selectInfo);
    }
  };

  // Xử lý khi thêm sự kiện mới
  const handleAddEvent = () => {
    const start = new Date();
    const end = new Date();
    end.setHours(end.getHours() + 1);

    // Tạo sự kiện mới
    const event: CalendarEvent = {
      id: '',
      title: '',
      start,
      end,
      allDay: false,
      extendedProps: {
        type: 'meeting',
      },
    };

    setNewEvent(event);
    setShowEventModal(true);
  };

  // Xử lý khi lưu sự kiện mới
  const handleSaveEvent = () => {
    if (newEvent && newEvent.title.trim()) {
      // Thêm sự kiện mới
      addEvent(newEvent);

      // Đóng modal
      setShowEventModal(false);
      setNewEvent(null);
    }
  };

  // Xử lý khi cập nhật trường của sự kiện mới
  const updateNewEventField = (
    field: keyof CalendarEvent,
    value: string | Date | boolean | undefined
  ) => {
    if (newEvent) {
      setNewEvent({
        ...newEvent,
        [field]: value,
      });
    }
  };

  // Xử lý khi cập nhật loại sự kiện
  const updateEventType = (
    type: 'meeting' | 'appointment' | 'deadline' | 'lunch' | 'workshop' | 'planning'
  ) => {
    if (newEvent) {
      setNewEvent({
        ...newEvent,
        className: `calendar-event-${type}`,
        extendedProps: {
          ...newEvent.extendedProps,
          type,
        },
      });
    }
  };

  // Danh sách các loại sự kiện
  const eventTypes = [
    { value: 'meeting', label: 'Cuộc họp', color: 'primary' },
    { value: 'appointment', label: 'Cuộc hẹn', color: 'blue-500' },
    { value: 'deadline', label: 'Hạn chót', color: 'red-500' },
    { value: 'lunch', label: 'Ăn trưa', color: 'green-500' },
    { value: 'workshop', label: 'Hội thảo', color: 'yellow-500' },
    { value: 'planning', label: 'Lập kế hoạch', color: 'purple-500' },
  ];

  // Xác định class cho calendar container
  const calendarClass = cn(
    'calendar-container',
    themeMode === 'dark' ? 'fc-theme-dark' : 'fc-theme-light',
    className
  );

  // Đảm bảo calendar được render đúng khi theme thay đổi
  useEffect(() => {
    const timer = setTimeout(() => {
      if (calendarRef.current) {
        const api = calendarRef.current.getApi();
        api.updateSize();
      }
    }, 100);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [themeMode]);

  return (
    <div className="calendar-wrapper">
      {/* Toolbar với các tùy chọn lọc */}
      <CalendarToolbar
        weekends={showWeekends}
        onWeekendsToggle={setShowWeekends}
        eventTypes={eventTypes}
        className="mb-4"
      />

      {/* Calendar container */}
      <div className={calendarClass}>
        {/* Header với các nút điều hướng */}
        <CalendarHeader
          title={calendarTitle}
          currentView={currentView}
          onPrev={handlePrev}
          onNext={handleNext}
          onToday={handleToday}
          onViewChange={handleViewChange}
          onAddEvent={handleAddEvent}
        />

        {/* FullCalendar component */}
        <FullCalendar
          ref={calendarRef}
          key={`calendar-${themeMode}`}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
          initialView={initialView}
          initialDate={initialDate}
          locale={viLocale}
          headerToolbar={false}
          events={calendarEvents}
          editable={editable}
          selectable={selectable}
          selectMirror={true}
          dayMaxEvents={true}
          weekends={showWeekends}
          allDaySlot={allDaySlot}
          height={height}
          select={handleDateSelect}
          eventClick={handleEventClickInternal}
          eventChange={handleEventChangeInternal}
          eventTimeFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }}
          slotLabelFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }}
          eventDidMount={info => {
            // Đảm bảo class được áp dụng đúng
            if (info.event.extendedProps?.type) {
              const type = info.event.extendedProps.type;
              info.el.classList.add(`calendar-event-${type}`);
            }
          }}
          themeSystem="standard"
          firstDay={1} // Bắt đầu từ thứ 2
          fixedWeekCount={false} // Số tuần hiển thị linh hoạt theo tháng
          showNonCurrentDates={false} // Ẩn các ngày không thuộc tháng hiện tại
        />
      </div>

      {/* Form thêm/sửa sự kiện */}
      {showEventModal && newEvent && (
        <EventForm
          event={newEvent}
          eventTypes={eventTypes}
          isOpen={showEventModal}
          title="Thêm sự kiện"
          onClose={() => setShowEventModal(false)}
          onSave={handleSaveEvent}
          onUpdateField={updateNewEventField}
          onUpdateEventType={updateEventType}
        />
      )}
    </div>
  );
};

export default CalendarWrapper;
