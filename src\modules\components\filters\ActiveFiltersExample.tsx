import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Typography,
  Button,
  SearchBar,
  Select,
  DatePicker,
} from '@/shared/components/common';
import { SortDirection } from '@/shared/dto/request/query.dto';

import { ActiveFilters, FilterTag } from './index';

/**
 * Component ví dụ minh họa cách sử dụng ActiveFilters
 */
const ActiveFiltersExample: React.FC = () => {
  const { t } = useTranslation(['common']);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterValue, setFilterValue] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [sortBy, setSortBy] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection | null>(null);
  const [customTags, setCustomTags] = useState<FilterTag[]>([
    {
      id: 'price',
      label: t('common:price'),
      value: '100.000đ - 500.000đ',
      onRemove: () => {
        setCustomTags(prev => prev.filter(tag => tag.id !== 'price'));
      },
    },
  ]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
  };

  const handleFilterChange = (value: string | number | string[] | number[]) => {
    setFilterValue(value as string);
  };

  const handleClearFilter = () => {
    setFilterValue('all');
  };

  const handleStartDateChange = (date: Date | null) => {
    setDateRange([date, dateRange[1]]);
  };

  const handleEndDateChange = (date: Date | null) => {
    setDateRange([dateRange[0], date]);
  };

  const handleClearDateRange = () => {
    setDateRange([null, null]);
  };

  const handleSortChange = (field: string, direction: SortDirection) => {
    setSortBy(field);
    setSortDirection(direction);
  };

  const handleClearSort = () => {
    setSortBy(null);
    setSortDirection(null);
  };

  const handleClearAll = () => {
    handleClearSearch();
    handleClearFilter();
    handleClearDateRange();
    handleClearSort();
    setCustomTags([]);
  };

  const getFilterLabel = () => {
    if (filterValue === 'active') {
      return t('common:active');
    }
    if (filterValue === 'inactive') {
      return t('common:inactive');
    }
    return filterValue;
  };

  return (
    <Card className="p-4">
      <Typography variant="h5" className="mb-4">
        ActiveFilters Component Example
      </Typography>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <Typography variant="subtitle1" className="mb-2">
            Search
          </Typography>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            onSubmit={handleSearch}
            placeholder={t('common:search')}
            maxWidth="100%"
            variant="flat"
            searchOnEnter={true}
          />
        </div>

        <div>
          <Typography variant="subtitle1" className="mb-2">
            Filter
          </Typography>
          <Select
            options={[
              { value: 'all', label: t('common:all') },
              { value: 'active', label: t('common:active') },
              { value: 'inactive', label: t('common:inactive') },
            ]}
            value={filterValue}
            onChange={handleFilterChange}
            placeholder={t('common:selectFilter')}
          />
        </div>

        <div>
          <Typography variant="subtitle1" className="mb-2">
            Sort
          </Typography>
          <div className="flex space-x-2">
            <Select
              options={[
                { value: 'name', label: t('common:name') },
                { value: 'createdAt', label: t('common:createdAt') },
                { value: 'updatedAt', label: t('common:updatedAt') },
              ]}
              value={sortBy || ''}
              onChange={value =>
                handleSortChange(value as string, sortDirection || SortDirection.ASC)
              }
              placeholder={t('common:selectField')}
            />
            <Select
              options={[
                { value: SortDirection.ASC, label: t('common:ascending') },
                { value: SortDirection.DESC, label: t('common:descending') },
              ]}
              value={sortDirection || ''}
              onChange={value => handleSortChange(sortBy || 'name', value as SortDirection)}
              placeholder={t('common:selectDirection')}
              disabled={!sortBy}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <Typography variant="subtitle1" className="mb-2">
            Date Range
          </Typography>
          <div className="flex space-x-2">
            <DatePicker
              value={dateRange[0]}
              onChange={handleStartDateChange}
              placeholder={t('common:startDate')}
              fullWidth={false}
            />
            <DatePicker
              value={dateRange[1]}
              onChange={handleEndDateChange}
              placeholder={t('common:endDate')}
              fullWidth={false}
              minDate={dateRange[0] || undefined}
            />
          </div>
        </div>

        <div>
          <Typography variant="subtitle1" className="mb-2">
            Custom Tag
          </Typography>
          <Button
            variant="outline"
            onClick={() => {
              if (customTags.length === 0) {
                setCustomTags([
                  {
                    id: 'price',
                    label: t('common:price'),
                    value: '100.000đ - 500.000đ',
                    onRemove: () => {
                      setCustomTags([]);
                    },
                  },
                ]);
              } else {
                setCustomTags([]);
              }
            }}
          >
            {customTags.length === 0 ? t('common:addCustomTag') : t('common:removeCustomTag')}
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <Typography variant="subtitle1" className="mb-2">
          Active Filters
        </Typography>
        <ActiveFilters
          searchTerm={searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={filterValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          dateRange={dateRange}
          onClearDateRange={handleClearDateRange}
          sortBy={sortBy}
          sortDirection={sortDirection}
          onClearSort={handleClearSort}
          customTags={customTags}
          onClearAll={handleClearAll}
        />
      </div>

      <div className="mt-8">
        <Typography variant="subtitle1" className="mb-2">
          Actions
        </Typography>
        <div className="flex space-x-2">
          <Button variant="primary" onClick={handleClearAll}>
            {t('common:clearAllFilters')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ActiveFiltersExample;
