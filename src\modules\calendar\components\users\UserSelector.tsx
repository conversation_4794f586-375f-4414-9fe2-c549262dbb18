import React, { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

import { TypeaheadSelect } from '@/shared/components/common/Select';

/**
 * Interface cho thông tin người dùng
 */
export interface User {
  /**
   * ID người dùng
   */
  id: string;

  /**
   * Tên đầy đủ
   */
  fullName: string;

  /**
   * Email
   */
  email: string;

  /**
   * URL ảnh đại diện
   */
  avatarUrl?: string;

  /**
   * Chức vụ
   */
  position?: string;

  /**
   * ID phòng ban
   */
  departmentId?: number;

  /**
   * Tên phòng ban
   */
  departmentName?: string;
}

/**
 * Interface cho nhóm người dùng
 */
export interface UserGroup {
  /**
   * ID nhóm
   */
  id: string;

  /**
   * Tên nhóm
   */
  name: string;

  /**
   * Danh sách người dùng trong nhóm
   */
  users: User[];
}

/**
 * Interface cho option của TypeaheadSelect
 */
export interface UserOption {
  /**
   * Giá trị
   */
  value: string;

  /**
   * Nhãn
   */
  label: string;

  /**
   * Thông tin người dùng
   */
  user?: User;

  /**
   * Thông tin nhóm
   */
  group?: UserGroup;

  /**
   * Là nhóm hay không
   */
  isGroup?: boolean;
}

/**
 * Props cho UserSelector
 */
export interface UserSelectorProps {
  /**
   * Người dùng đã chọn
   */
  selectedUsers?: User[];

  /**
   * Callback khi thay đổi lựa chọn
   */
  onChange?: (users: User[]) => void;

  /**
   * Danh sách người dùng
   */
  users?: User[];

  /**
   * Danh sách nhóm người dùng
   */
  groups?: UserGroup[];

  /**
   * Cho phép chọn nhiều người dùng
   */
  multiple?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Hiển thị phòng ban
   */
  showDepartment?: boolean;

  /**
   * Hiển thị chức vụ
   */
  showPosition?: boolean;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Số lượng gợi ý hiển thị
   */
  maxSuggestions?: number;
}

/**
 * Component UserSelector - Chọn người dùng với gợi ý khi gõ
 *
 * @example
 * ```tsx
 * // Chọn một người dùng
 * const [selectedUser, setSelectedUser] = useState<User | null>(null);
 *
 * <UserSelector
 *   label="Người tham gia"
 *   selectedUsers={selectedUser ? [selectedUser] : []}
 *   onChange={(users) => setSelectedUser(users[0] || null)}
 *   users={usersList}
 *   placeholder="Nhập tên người dùng..."
 * />
 *
 * // Chọn nhiều người dùng
 * const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
 *
 * <UserSelector
 *   label="Người tham gia"
 *   selectedUsers={selectedUsers}
 *   onChange={setSelectedUsers}
 *   users={usersList}
 *   multiple
 *   placeholder="Nhập tên người dùng..."
 * />
 * ```
 */
const UserSelector = forwardRef<HTMLInputElement, UserSelectorProps>(
  (
    {
      selectedUsers = [],
      onChange,
      users = [],
      groups = [],
      multiple = false,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      maxSuggestions = 10,
    },
    ref
  ) => {
    const { t } = useTranslation();

    // Chuyển đổi danh sách người dùng thành options cho TypeaheadSelect
    const userOptions = React.useMemo<UserOption[]>(() => {
      const options: UserOption[] = users.map(user => ({
        value: user.id,
        label: user.fullName,
        user,
      }));

      // Thêm options từ các nhóm nếu có
      if (groups.length > 0) {
        for (const group of groups) {
          options.push({
            value: `group-${group.id}`,
            label: `${group.name} (${group.users.length})`,
            isGroup: true,
            group,
          });
        }
      }

      return options;
    }, [users, groups]);

    // Chuyển đổi selectedUsers thành giá trị cho TypeaheadSelect
    const selectedValues = React.useMemo(() => {
      return selectedUsers.map(user => user.id);
    }, [selectedUsers]);

    // Xử lý khi thay đổi giá trị
    const handleChange = (value: string | string[]) => {
      if (!onChange) {return;}

      const values = Array.isArray(value) ? value : [value];
      const selectedUsersList = values
        .map(id => users.find(user => user.id === id))
        .filter(Boolean) as User[];

      onChange(selectedUsersList);
    };

    return (
      <TypeaheadSelect
        ref={ref}
        value={multiple ? selectedValues : selectedValues[0] || ''}
        onChange={handleChange}
        options={userOptions}
        multiple={multiple}
        placeholder={placeholder || t('calendar.selectUsers', 'Chọn người dùng')}
        label={label || t('calendar.participants', 'Người tham gia')}
        disabled={disabled}
        name={name}
        id={id}
        className={className}
        error={error}
        helperText={helperText}
        size={size}
        fullWidth={fullWidth}
        highlightMatch={true}
        maxSuggestions={maxSuggestions}
      />
    );
  }
);

UserSelector.displayName = 'UserSelector';

export default UserSelector;
