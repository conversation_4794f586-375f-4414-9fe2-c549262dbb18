import { Repository } from 'typeorm';

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { SocialAccount } from '../entities/social-account.entity';
import { SocialProvider } from '../enum';

/**
 * Repository for social account entity
 */
@Injectable()
export class SocialAccountRepository {
  private readonly logger = new Logger(SocialAccountRepository.name);

  constructor(
    @InjectRepository(SocialAccount)
    private readonly repository: Repository<SocialAccount>,
  ) {}

  /**
   * Find social account by provider and social ID
   * @param provider Social provider (Google, Facebook, Zalo)
   * @param socialId ID of the user on the social platform
   * @returns Social account or null if not found
   */
  async findBySocialId(
    provider: SocialProvider,
    socialId: string,
  ): Promise<SocialAccount | null> {
    return this.repository.findOne({
      where: {
        provider,
        socialId,
      },
    });
  }

  /**
   * Find social accounts by user ID
   * @param userId User ID
   * @returns List of social accounts
   */
  async findByUserId(userId: number): Promise<SocialAccount[]> {
    return this.repository.find({
      where: {
        userId,
      },
    });
  }

  /**
   * Find social account by user ID and provider
   * @param userId User ID
   * @param provider Social provider
   * @returns Social account or null if not found
   */
  async findByUserIdAndProvider(
    userId: number,
    provider: SocialProvider,
  ): Promise<SocialAccount | null> {
    return this.repository.findOne({
      where: {
        userId,
        provider,
      },
    });
  }

  /**
   * Create a new social account
   * @param data Social account data
   * @returns Created social account
   */
  async create(data: Partial<SocialAccount>): Promise<SocialAccount> {
    const socialAccount = this.repository.create(data);
    return this.repository.save(socialAccount);
  }

  /**
   * Update social account
   * @param id Social account ID
   * @param data Updated social account data
   * @returns Updated social account or null if not found
   */
  async update(id: number, data: Partial<SocialAccount>): Promise<SocialAccount | null> {
    await this.repository.update(id, data);
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Delete social account
   * @param id Social account ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected ? result.affected > 0 : false;
  }

  /**
   * Delete social account by user ID and provider
   * @param userId User ID
   * @param provider Social provider
   * @returns True if deleted, false if not found
   */
  async deleteByUserIdAndProvider(
    userId: number,
    provider: SocialProvider,
  ): Promise<boolean> {
    const result = await this.repository.delete({
      userId,
      provider,
    });
    return result.affected ? result.affected > 0 : false;
  }
}
