import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';

import {
  AffiliateOrderDto,
  AffiliateOrderStatus,
  AffiliateOrderType,
} from '../types/affiliate.types';

/**
 * Trang quản lý đơn hàng affiliate
 */
const AffiliateOrderListPage: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');

  // Không cần form cho trang đơn hàng

  // Mock data cho Affiliate Order
  const orderData = useMemo<AffiliateOrderDto[]>(
    () => [
      {
        id: 1,
        publisherId: 1,
        publisherName: 'Nguyễn <PERSON>n <PERSON>',
        userId: 201,
        userName: 'Kh<PERSON>ch hàng 1',
        orderNumber: 'ORD-001',
        orderType: AffiliateOrderType.POINT_PURCHASE,
        orderAmount: 1000000,
        commissionAmount: 50000,
        commissionRate: 5,
        status: AffiliateOrderStatus.COMPLETED,
        createdAt: '2023-07-15',
        updatedAt: '2023-07-15',
      },
      {
        id: 2,
        publisherId: 1,
        publisherName: 'Nguyễn Văn A',
        userId: 202,
        userName: 'Khách hàng 2',
        orderNumber: 'ORD-002',
        orderType: AffiliateOrderType.SUBSCRIPTION,
        orderAmount: 2000000,
        commissionAmount: 100000,
        commissionRate: 5,
        status: AffiliateOrderStatus.COMPLETED,
        createdAt: '2023-07-16',
        updatedAt: '2023-07-16',
      },
      {
        id: 3,
        publisherId: 2,
        publisherName: 'Trần Thị B',
        userId: 203,
        userName: 'Khách hàng 3',
        orderNumber: 'ORD-003',
        orderType: AffiliateOrderType.SERVICE,
        orderAmount: 1500000,
        commissionAmount: 150000,
        commissionRate: 10,
        status: AffiliateOrderStatus.COMPLETED,
        createdAt: '2023-07-17',
        updatedAt: '2023-07-17',
      },
      {
        id: 4,
        publisherId: 3,
        publisherName: 'Lê Văn C',
        userId: 204,
        userName: 'Khách hàng 4',
        orderNumber: 'ORD-004',
        orderType: AffiliateOrderType.POINT_PURCHASE,
        orderAmount: 500000,
        commissionAmount: 25000,
        commissionRate: 5,
        status: AffiliateOrderStatus.PENDING,
        createdAt: '2023-07-18',
        updatedAt: '2023-07-18',
      },
      {
        id: 5,
        publisherId: 4,
        publisherName: 'Phạm Thị D',
        userId: 205,
        userName: 'Khách hàng 5',
        orderNumber: 'ORD-005',
        orderType: AffiliateOrderType.SUBSCRIPTION,
        orderAmount: 3000000,
        commissionAmount: 0,
        commissionRate: 5,
        status: AffiliateOrderStatus.CANCELLED,
        createdAt: '2023-07-19',
        updatedAt: '2023-07-19',
      },
    ],
    []
  );

  // Columns cho bảng
  const columns: TableColumn<AffiliateOrderDto>[] = [
    {
      key: 'orderNumber',
      title: t('affiliate:order.table.orderNumber'),
      dataIndex: 'orderNumber',
      width: '10%',
    },
    {
      key: 'publisherName',
      title: t('affiliate:order.table.publisherName'),
      dataIndex: 'publisherName',
      width: '15%',
    },
    {
      key: 'userName',
      title: t('affiliate:order.table.userName'),
      dataIndex: 'userName',
      width: '15%',
    },
    {
      key: 'orderType',
      title: t('affiliate:order.table.orderType'),
      dataIndex: 'orderType',
      width: '10%',
      render: (value: unknown) => {
        const type = value as AffiliateOrderType;
        let label = '';
        switch (type) {
          case AffiliateOrderType.POINT_PURCHASE:
            label = t('affiliate:order.type.pointPurchase');
            break;
          case AffiliateOrderType.SUBSCRIPTION:
            label = t('affiliate:order.type.subscription');
            break;
          case AffiliateOrderType.SERVICE:
            label = t('affiliate:order.type.service');
            break;
          default:
            label = type;
        }
        return <div>{label}</div>;
      },
    },
    {
      key: 'orderAmount',
      title: t('affiliate:order.table.orderAmount'),
      dataIndex: 'orderAmount',
      width: '10%',
      render: (value: unknown) => {
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN').format(value as number)} đ
          </div>
        );
      },
    },
    {
      key: 'commissionAmount',
      title: t('affiliate:order.table.commissionAmount'),
      dataIndex: 'commissionAmount',
      width: '10%',
      render: (value: unknown) => {
        return (
          <div className="text-right">
            {new Intl.NumberFormat('vi-VN').format(value as number)} đ
          </div>
        );
      },
    },
    {
      key: 'status',
      title: t('affiliate:order.table.status'),
      dataIndex: 'status',
      width: '10%',
      render: (value: unknown) => {
        const status = value as AffiliateOrderStatus;
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              status === AffiliateOrderStatus.COMPLETED
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : status === AffiliateOrderStatus.PENDING
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }`}
          >
            {status === AffiliateOrderStatus.COMPLETED
              ? t('affiliate:order.status.completed')
              : status === AffiliateOrderStatus.PENDING
                ? t('affiliate:order.status.pending')
                : t('affiliate:order.status.cancelled')}
          </div>
        );
      },
    },
    {
      key: 'createdAt',
      title: t('affiliate:order.table.createdAt'),
      dataIndex: 'createdAt',
      width: '10%',
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '10%',
      render: (_: unknown, record: AffiliateOrderDto) => (
        <div className="flex space-x-2">
          <Tooltip content={t('common:view')} position="top">
            <IconCard
              icon="eye"
              variant="default"
              size="sm"
              onClick={() => console.log('View', record.id)}
            />
          </Tooltip>
          {record.status === AffiliateOrderStatus.PENDING && (
            <>
              <Tooltip content={t('affiliate:order.action.approve')} position="top">
                <IconCard
                  icon="check"
                  variant="default"
                  size="sm"
                  onClick={() => console.log('Approve', record.id)}
                />
              </Tooltip>
              <Tooltip content={t('affiliate:order.action.cancel')} position="top">
                <IconCard
                  icon="x"
                  variant="default"
                  size="sm"
                  onClick={() => console.log('Cancel', record.id)}
                />
              </Tooltip>
            </>
          )}
        </div>
      ),
    },
  ];

  // Lọc dữ liệu
  const filteredData = useMemo(() => {
    return orderData.filter((order: AffiliateOrderDto) => {
      const matchesSearch =
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.publisherName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.userName.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filter === 'all' || order.status === filter;
      return matchesSearch && matchesFilter;
    });
  }, [orderData, searchTerm, filter]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'completed',
            label: t('affiliate:order.status.completed'),
            icon: 'check',
            onClick: () => setFilter(AffiliateOrderStatus.COMPLETED),
          },
          {
            id: 'pending',
            label: t('affiliate:order.status.pending'),
            icon: 'clock',
            onClick: () => setFilter(AffiliateOrderStatus.PENDING),
          },
          {
            id: 'cancelled',
            label: t('affiliate:order.status.cancelled'),
            icon: 'x',
            onClick: () => setFilter(AffiliateOrderStatus.CANCELLED),
          },
        ]}
      />

      <Card className="overflow-hidden">
        <Table columns={columns} data={filteredData} rowKey="id" pagination />
      </Card>
    </div>
  );
};

export default AffiliateOrderListPage;
