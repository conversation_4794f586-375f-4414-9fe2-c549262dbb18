import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button } from '@/shared/components/common';

import { mockUsers, mockUserGroups } from '../../data/mock-users';

import UserSelector, { User } from './UserSelector';

/**
 * Component demo cho UserSelector
 */
const UserSelectorDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho single select
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // State cho multiple select
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  // Xử lý khi chọn một người dùng
  const handleSingleSelect = (users: User[]) => {
    setSelectedUser(users[0] || null);
  };

  // <PERSON><PERSON> lý khi chọn nhiều người dùng
  const handleMultiSelect = (users: User[]) => {
    setSelectedUsers(users);
  };

  // Reset lựa chọn
  const handleReset = () => {
    setSelectedUser(null);
    setSelectedUsers([]);
  };

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('calendar.userSelectorDemo', 'Demo UserSelector')}
      </Typography>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Single Select */}
        <Card className="p-4">
          <Typography variant="h3" className="mb-4">
            {t('calendar.singleSelect', 'Chọn một người dùng')}
          </Typography>

          <UserSelector
            label={t('calendar.participant', 'Người tham gia')}
            selectedUsers={selectedUser ? [selectedUser] : []}
            onChange={handleSingleSelect}
            users={mockUsers}
            placeholder={t('calendar.searchUsers', 'Tìm kiếm người dùng...')}
            fullWidth
          />

          {selectedUser && (
            <div className="mt-4 p-3 bg-card-muted rounded-md">
              <Typography variant="h4" className="mb-2">
                {t('calendar.selectedUser', 'Người dùng đã chọn')}:
              </Typography>
              <pre className="text-sm overflow-auto">{JSON.stringify(selectedUser, null, 2)}</pre>
            </div>
          )}
        </Card>

        {/* Multiple Select */}
        <Card className="p-4">
          <Typography variant="h3" className="mb-4">
            {t('calendar.multipleSelect', 'Chọn nhiều người dùng')}
          </Typography>

          <UserSelector
            label={t('calendar.participants', 'Người tham gia')}
            selectedUsers={selectedUsers}
            onChange={handleMultiSelect}
            users={mockUsers}
            groups={mockUserGroups}
            multiple
            placeholder={t('calendar.searchUsers', 'Tìm kiếm người dùng...')}
            fullWidth
          />

          {selectedUsers.length > 0 && (
            <div className="mt-4 p-3 bg-card-muted rounded-md">
              <Typography variant="h4" className="mb-2">
                {t('calendar.selectedUsers', 'Người dùng đã chọn')} ({selectedUsers.length}):
              </Typography>
              <pre className="text-sm overflow-auto">{JSON.stringify(selectedUsers, null, 2)}</pre>
            </div>
          )}
        </Card>
      </div>

      <div className="mt-6 flex justify-center">
        <Button variant="outline" onClick={handleReset} className="px-4 py-2">
          {t('common.reset', 'Đặt lại')}
        </Button>
      </div>
    </div>
  );
};

export default UserSelectorDemo;
