import { Avatar, Dropdown, Icon, LanguageFlag, ThemeToggle } from '@/shared/components/common';
import { useLanguage } from '@/shared/contexts/language';
import { useTheme } from '@/shared/contexts/theme';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ViewBreadcrumb from './ViewBreadcrumb';

interface ViewHeaderProps {
  title: string;
  actions?: React.ReactNode;
}

const ViewHeader = ({ title, actions }: ViewHeaderProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { themeMode, toggleTheme } = useTheme();
  const theme = themeMode === 'custom' ? 'light' : themeMode;
  const { language, setLanguage, availableLanguages } = useLanguage();

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle language selection dropdown
      const langDropdown = document.getElementById('language-selection-dropdown');
      const langTrigger = document.getElementById('language-trigger');

      if (
        langDropdown &&
        !langDropdown.contains(event.target as Node) &&
        langTrigger &&
        !langTrigger.contains(event.target as Node)
      ) {
        langDropdown.classList.add('hidden');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sử dụng component LanguageFlag thay vì hàm renderLanguageFlag

  // No separate language dropdown items needed anymore

  // Create profile dropdown items
  const profileItems = [
    {
      id: 'profile',
      label: t('common.profile'),
      onClick: () => navigate('/profile'),
      icon: <Icon name="user" size="sm" />,
    },
    {
      id: 'language-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name="language" size="sm" className="mr-2" />
            <span className="mr-3">{t('common.language')}</span>
          </div>

          {/* Current language flag */}
          <LanguageFlag code={language as 'vi' | 'en' | 'zh'} isSelected={true} />
        </div>
      ),
      subItems: availableLanguages.map(lang => ({
        id: `lang-${lang.code}`,
        label: (
          <div className="flex items-center">
            <LanguageFlag code={lang.code as 'vi' | 'en' | 'zh'} />
            <span className="ml-2">{lang.name}</span>
            {language === lang.code && (
              <div className="ml-2 text-primary">
                <Icon name="check" size="sm" fill />
              </div>
            )}
          </div>
        ),
        onClick: () => setLanguage(lang.code as 'vi' | 'en' | 'zh'),
      })),
    },
    {
      id: 'theme-divider',
      divider: true,
    },
    {
      id: 'theme-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name={theme === 'light' ? 'sun' : 'moon'} size="sm" className="mr-2" />
            <span className="mr-3">{t('common.theme')}</span>
          </div>

          <ThemeToggle
            theme={theme}
            onToggle={toggleTheme}
            lightText={t('common.light')}
            darkText={t('common.dark')}
          />
        </div>
      ),
      onClick: () => {},
    },
    {
      id: 'settings-divider',
      divider: true,
    },
    {
      id: 'settings',
      label: t('common.settings'),
      onClick: () => console.log('Settings clicked'),
      icon: <Icon name="settings" size="sm" />,
    },
    {
      id: 'logout-divider',
      divider: true,
    },
    {
      id: 'logout',
      label: t('common.logout'),
      onClick: () => {},
      icon: <Icon name="logout" size="sm" />,
    },
  ];

  return (
    <div className="flex items-center justify-between p-3 px-4 w-full max-w-full overflow-hidden">
      <div className="flex items-center overflow-hidden">
        <ViewBreadcrumb title={title} />
        {actions && <div className="ml-4 overflow-hidden">{actions}</div>}
      </div>

      <div className="flex items-center space-x-4 relative z-40">
        {/* User dropdown */}
        <Dropdown
          trigger={
            <div className="cursor-pointer">
              <Avatar
                src="\src\shared\assets\images\avatar-user.JPG"
                alt="User"
                size="md"
                status="online"
              />
            </div>
          }
          items={profileItems}
          placement="bottom-right"
          width="w-56"
        />
      </div>
    </div>
  );
};

export default ViewHeader;
