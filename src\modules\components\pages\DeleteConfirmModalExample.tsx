import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Container, Typography, Button, DeleteConfirmModal } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

/**
 * Trang ví dụ về cách sử dụng DeleteConfirmModal
 */
const DeleteConfirmModalExample: React.FC = () => {
  const { t } = useTranslation();
  const [isBasicModalOpen, setIsBasicModalOpen] = useState(false);
  const [isCustomModalOpen, setIsCustomModalOpen] = useState(false);
  const [isLoadingModalOpen, setIsLoadingModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Xử lý xóa cơ bản
  const handleBasicDelete = () => {
    // Thực hiện xóa
    console.log('Đã xóa mục');
    setIsBasicModalOpen(false);
  };

  // Xử lý xóa với tên mục
  const handleCustomDelete = () => {
    console.log('Đã xóa người dùng "John Doe"');
    setIsCustomModalOpen(false);
  };

  // Xử lý xóa với loading
  const handleLoadingDelete = () => {
    setIsLoading(true);
    // Giả lập API call
    setTimeout(() => {
      console.log('Đã xóa sau khi loading');
      setIsLoading(false);
      setIsLoadingModalOpen(false);
    }, 2000);
  };

  return (
    <Container>
      <Typography variant="h4" className="mb-6">
        {t('components.deleteConfirmModal.title', 'Modal xác nhận xóa')}
      </Typography>

      <Typography variant="body1" className="mb-6">
        {t(
          'components.deleteConfirmModal.description',
          'Modal xác nhận xóa là một component dùng chung để hiển thị hộp thoại xác nhận trước khi xóa dữ liệu.'
        )}
      </Typography>

      {/* Ví dụ cơ bản */}
      <ComponentDemo
        title={t('components.deleteConfirmModal.basic.title', 'Cơ bản')}
        description={t(
          'components.deleteConfirmModal.basic.description',
          'Modal xác nhận xóa cơ bản với các thông báo mặc định.'
        )}
        code={`import { useState } from 'react';
import { Button, DeleteConfirmModal } from '@/shared/components/common';

// Trong component
const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

const handleDelete = () => {
  // Thực hiện xóa
  console.log('Đã xóa mục');
  setIsDeleteModalOpen(false);
};

// Trong JSX
<>
  <Button variant="danger" onClick={() => setIsDeleteModalOpen(true)}>
    Xóa mục
  </Button>
  
  <DeleteConfirmModal
    isOpen={isDeleteModalOpen}
    onClose={() => setIsDeleteModalOpen(false)}
    onConfirm={handleDelete}
  />
</>`}
      >
        <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
          <Button variant="danger" onClick={() => setIsBasicModalOpen(true)}>
            {t('components.deleteConfirmModal.basic.button', 'Xóa mục')}
          </Button>

          <DeleteConfirmModal
            isOpen={isBasicModalOpen}
            onClose={() => setIsBasicModalOpen(false)}
            onConfirm={handleBasicDelete}
          />
        </div>
      </ComponentDemo>

      {/* Ví dụ tùy chỉnh */}
      <ComponentDemo
        title={t('components.deleteConfirmModal.custom.title', 'Tùy chỉnh')}
        description={t(
          'components.deleteConfirmModal.custom.description',
          'Modal xác nhận xóa với nội dung tùy chỉnh và tên mục cần xóa.'
        )}
        code={`import { useState } from 'react';
import { Button, DeleteConfirmModal } from '@/shared/components/common';

// Trong component
const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

const handleDelete = () => {
  console.log('Đã xóa người dùng "John Doe"');
  setIsDeleteModalOpen(false);
};

// Trong JSX
<>
  <Button variant="danger" onClick={() => setIsDeleteModalOpen(true)}>
    Xóa người dùng
  </Button>
  
  <DeleteConfirmModal
    isOpen={isDeleteModalOpen}
    onClose={() => setIsDeleteModalOpen(false)}
    onConfirm={handleDelete}
    title="Xóa người dùng"
    itemName="John Doe"
    description="Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến người dùng này sẽ bị xóa vĩnh viễn."
  />
</>`}
      >
        <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
          <Button variant="danger" onClick={() => setIsCustomModalOpen(true)}>
            {t('components.deleteConfirmModal.custom.button', 'Xóa người dùng')}
          </Button>

          <DeleteConfirmModal
            isOpen={isCustomModalOpen}
            onClose={() => setIsCustomModalOpen(false)}
            onConfirm={handleCustomDelete}
            title={t('components.deleteConfirmModal.custom.modalTitle', 'Xóa người dùng')}
            itemName="John Doe"
            description={t(
              'components.deleteConfirmModal.custom.modalDescription',
              'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến người dùng này sẽ bị xóa vĩnh viễn.'
            )}
          />
        </div>
      </ComponentDemo>

      {/* Ví dụ với loading */}
      <ComponentDemo
        title={t('components.deleteConfirmModal.loading.title', 'Với trạng thái loading')}
        description={t(
          'components.deleteConfirmModal.loading.description',
          'Modal xác nhận xóa với trạng thái loading khi đang xử lý.'
        )}
        code={`import { useState } from 'react';
import { Button, DeleteConfirmModal } from '@/shared/components/common';

// Trong component
const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
const [isLoading, setIsLoading] = useState(false);

const handleDelete = () => {
  setIsLoading(true);
  // Giả lập API call
  setTimeout(() => {
    console.log('Đã xóa sau khi loading');
    setIsLoading(false);
    setIsDeleteModalOpen(false);
  }, 2000);
};

// Trong JSX
<>
  <Button variant="danger" onClick={() => setIsDeleteModalOpen(true)}>
    Xóa với loading
  </Button>
  
  <DeleteConfirmModal
    isOpen={isDeleteModalOpen}
    onClose={() => setIsDeleteModalOpen(false)}
    onConfirm={handleDelete}
    isLoading={isLoading}
  />
</>`}
      >
        <div className="p-4 bg-gray-100 dark:bg-dark-light rounded flex justify-center">
          <Button variant="danger" onClick={() => setIsLoadingModalOpen(true)}>
            {t('components.deleteConfirmModal.loading.button', 'Xóa với loading')}
          </Button>

          <DeleteConfirmModal
            isOpen={isLoadingModalOpen}
            onClose={() => setIsLoadingModalOpen(false)}
            onConfirm={handleLoadingDelete}
            isLoading={isLoading}
          />
        </div>
      </ComponentDemo>
    </Container>
  );
};

export default DeleteConfirmModalExample;
