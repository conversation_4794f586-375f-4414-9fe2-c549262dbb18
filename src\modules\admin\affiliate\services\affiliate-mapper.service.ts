import {
  PublisherDto,
  AffiliateRankDto,
  AffiliateOrderDto,
  PaginatedPublisherResult,
  PaginatedAffiliateRankResult,
  PaginatedAffiliateOrderResult,
  mapApiStatusToPublisherStatus,
  mapApiOrderStatusToUiOrderStatus,
  mapApiOrderTypeToUiOrderType,
  AffiliateRankStatus,
} from '../types/affiliate.types';
/**
 * Service chuyển đổi dữ liệu giữa API và UI
 */
import {
  AffiliateAccountDto,
  AffiliateRankDto as ApiAffiliateRankDto,
  AffiliateOrderDto as ApiAffiliateOrderDto,
  PaginatedAffiliateAccountResult,
  PaginatedAffiliateRankResult as ApiPaginatedAffiliateRankResult,
  PaginatedAffiliateOrderResult as ApiPaginatedAffiliateOrderResult,
} from '../types/api.types';

/**
 * Chuyển đổi từ AffiliateAccountDto sang PublisherDto
 * @param account Dữ liệu tài khoản affiliate từ API
 * @returns Dữ liệu publisher cho UI
 */
export const mapAccountToPublisher = (account: AffiliateAccountDto): PublisherDto => {
  return {
    id: account.id,
    userId: account.user.id,
    userName: account.user.fullName,
    userEmail: account.user.email,
    userPhone: account.user.phoneNumber || '',
    referralCode: account.referralCode,
    totalReferrals: account.performance, // Sử dụng performance làm số lượt giới thiệu
    totalCommission: account.totalEarnings,
    status: mapApiStatusToPublisherStatus(account.status),
    createdAt: new Date(account.createdAt * 1000).toISOString().split('T')[0], // Chuyển timestamp sang date string
    updatedAt: account.updatedAt
      ? new Date(account.updatedAt * 1000).toISOString().split('T')[0]
      : '',
  };
};

/**
 * Chuyển đổi từ PaginatedAffiliateAccountResult sang PaginatedPublisherResult
 * @param accounts Dữ liệu danh sách tài khoản affiliate từ API
 * @returns Dữ liệu danh sách publisher cho UI
 */
export const mapAccountsToPaginatedPublishers = (
  accounts: PaginatedAffiliateAccountResult
): PaginatedPublisherResult => {
  return {
    items: accounts.items.map(mapAccountToPublisher),
    meta: accounts.meta,
  };
};

/**
 * Chuyển đổi từ ApiAffiliateRankDto sang AffiliateRankDto
 * @param rank Dữ liệu rank affiliate từ API
 * @returns Dữ liệu rank affiliate cho UI
 */
export const mapApiRankToUiRank = (rank: ApiAffiliateRankDto): AffiliateRankDto => {
  return {
    id: rank.id,
    name: rank.rankName,
    description: rank.description || '',
    minReferrals: rank.minCondition,
    commissionRate: rank.commission,
    status: rank.isActive ? AffiliateRankStatus.ACTIVE : AffiliateRankStatus.INACTIVE,
    createdAt: new Date(rank.createdAt * 1000).toISOString().split('T')[0],
    updatedAt: rank.updatedAt ? new Date(rank.updatedAt * 1000).toISOString().split('T')[0] : '',
  };
};

/**
 * Chuyển đổi từ ApiPaginatedAffiliateRankResult sang PaginatedAffiliateRankResult
 * @param ranks Dữ liệu danh sách rank affiliate từ API
 * @returns Dữ liệu danh sách rank affiliate cho UI
 */
export const mapApiRanksToPaginatedUiRanks = (
  ranks: ApiPaginatedAffiliateRankResult
): PaginatedAffiliateRankResult => {
  return {
    items: ranks.items.map(mapApiRankToUiRank),
    meta: ranks.meta,
  };
};

/**
 * Chuyển đổi từ ApiAffiliateOrderDto sang AffiliateOrderDto
 * @param order Dữ liệu đơn hàng affiliate từ API
 * @returns Dữ liệu đơn hàng affiliate cho UI
 */
export const mapApiOrderToUiOrder = (order: ApiAffiliateOrderDto): AffiliateOrderDto => {
  // Giả định cấu trúc của ApiAffiliateOrderDto, cần điều chỉnh theo thực tế
  return {
    id: order.id,
    orderNumber: order.orderNumber,
    publisherId: order.publisherId,
    publisherName: order.publisherName,
    userId: order.userId,
    userName: order.userName,
    orderType: mapApiOrderTypeToUiOrderType(order.orderType),
    orderAmount: order.orderAmount,
    commissionAmount: order.commissionAmount,
    commissionRate: order.commissionRate,
    status: mapApiOrderStatusToUiOrderStatus(order.status),
    createdAt: new Date(order.createdAt).toISOString().split('T')[0],
    updatedAt: new Date(order.updatedAt).toISOString().split('T')[0],
  };
};

/**
 * Chuyển đổi từ ApiPaginatedAffiliateOrderResult sang PaginatedAffiliateOrderResult
 * @param orders Dữ liệu danh sách đơn hàng affiliate từ API
 * @returns Dữ liệu danh sách đơn hàng affiliate cho UI
 */
export const mapApiOrdersToPaginatedUiOrders = (
  orders: ApiPaginatedAffiliateOrderResult
): PaginatedAffiliateOrderResult => {
  return {
    items: orders.items.map(mapApiOrderToUiOrder),
    meta: orders.meta,
  };
};
