import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';

import logoImage from '@/shared/assets/images/logo/logo.png';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveImage,
  Alert,
  OTPInput,
} from '@/shared/components/common';

import CountdownTimer from '../components/CountdownTimer';
import { useAuth } from '../hooks/useAuth';
import { useCompany } from '../hooks/useCompany';
import { useCompanyAuth } from '../hooks/useCompanyAuth';
import { UserStatus } from '../types/company-auth.types';

/**
 * Verify email page component
 */
const VerifyEmailPage: React.FC = () => {
  const { t } = useTranslation('auth');
  const navigate = useNavigate();
  const { registerInfo, saveCompany, clearRegisterData } = useCompany();
  const { verifyCompanyEmail } = useCompanyAuth();
  const { setAuth } = useAuth();
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);

  // Kiểm tra dữ liệu xác thực từ Redux
  useEffect(() => {
    console.log('VerifyEmailPage mounted, register info:', registerInfo);

    if (!registerInfo?.otpToken) {
      // Redirect to login if no token
      navigate('/auth');
    }
  }, [registerInfo, navigate]);

  // Kiểm tra thời gian hết hạn của OTP
  useEffect(() => {
    if (registerInfo?.expiresAt) {
      // Kiểm tra ngay lập tức
      const checkExpiration = () => {
        const now = Date.now();
        console.log('Checking expiration:', {
          now,
          expiresAt: registerInfo.expiresAt,
          diff: registerInfo.expiresAt - now,
        });

        if (now >= registerInfo.expiresAt) {
          setIsOtpExpired(true);
        } else {
          setIsOtpExpired(false);
        }
      };

      // Kiểm tra ngay lập tức
      checkExpiration();

      // Kiểm tra mỗi giây
      const timer = setInterval(checkExpiration, 1000);

      return () => clearInterval(timer);
    }

    // Trả về một hàm cleanup rỗng nếu không có expiresAt
    return () => {};
  }, [registerInfo]);

  // Xử lý khi OTP thay đổi - được sử dụng trực tiếp trong OTPInput
  // Không cần hàm riêng vì đã sử dụng setOtp trực tiếp

  // Hàm xác thực OTP
  const handleVerifyOtp = (otpValue: string = otp) => {
    if (!registerInfo?.otpToken) {return;}
    if (!otpValue || otpValue.length !== 6) {
      setError(t('invalidOtp', 'Mã OTP không hợp lệ. Vui lòng nhập đủ 6 chữ số.'));
      return;
    }

    // Gọi API xác thực OTP
    verifyCompanyEmail.mutate(
      {
        token: registerInfo.otpToken,
        otp: otpValue,
      },
      {
        onSuccess: async response => {
          if (response.code === 200) {
            // Hiển thị thông báo thành công
            setError(null);

            // Lưu thông tin đăng nhập vào Redux nếu có
            console.log('Kiểm tra response.result:', response.result);
            console.log('response.result.accessToken:', response.result?.accessToken);
            console.log('response.result.company:', response.result?.company);

            if (response.result) {
              if (response.result.accessToken && response.result.company) {
                console.log('Xác thực thành công, lưu thông tin công ty:', response.result);
                console.log('Đang chuyển hướng đến trang chính...');

                try {
                  // Lưu token vào localStorage trước
                  localStorage.setItem('token', response.result.accessToken);
                  console.log('Token saved to localStorage:', response.result.accessToken);

                  // Lưu thông tin công ty vào Redux
                  console.log('Lưu thông tin công ty vào Redux...', response.result.company);
                  await saveCompany(response.result.company);

                  // Lưu thông tin đăng nhập vào Redux auth
                  console.log('Lưu thông tin đăng nhập vào Redux auth...');
                  const authData = {
                    accessToken: response.result.accessToken,
                    expiresIn: 86400, // Mặc định 1 ngày nếu không có thông tin
                    user: {
                      id: String(response.result.company.id), // Chuyển đổi sang string
                      email: response.result.company.companyEmail,
                      fullName: response.result.company.companyName,
                      role: 'COMPANY_ADMIN',
                      status: UserStatus.ACTIVE, // Sử dụng enum
                    },
                  };
                  console.log('Auth data:', authData);
                  await setAuth(authData);

                  // Xóa thông tin đăng ký khỏi Redux vì không cần nữa
                  console.log('Xóa thông tin đăng ký khỏi Redux...');
                  await clearRegisterData();

                  // Chuyển hướng đến trang chính
                  console.log('Đã lưu tất cả dữ liệu, chuyển hướng đến trang chính...');
                  navigate('/');
                } catch (error) {
                  console.error('Lỗi khi lưu dữ liệu:', error);
                  // Nếu có lỗi, vẫn chuyển hướng đến trang chính
                  navigate('/');
                }

                return;
              }
            }

            // Nếu không có thông tin đăng nhập, chuyển hướng đến trang đăng nhập
            navigate('/auth');
          }
        },
        onError: error => {
          console.error('Verify OTP error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('verifyOtpError', 'Xác thực OTP thất bại');

          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as { response: { data?: { message?: string } } };
            if (axiosError.response.data?.message) {
              errorMsg = axiosError.response.data.message;
            }
          }

          setError(errorMsg);
        },
      }
    );
  };

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-8">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-16">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-full object-contain max-w-[70%]"
          />
        </div>
      </div>

      <div className="space-y-6 text-center">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <Icon name="mail" size="lg" className="text-green-500" />
          </div>
        </div>

        {error && <Alert type="error" message={error} closable onClose={() => setError(null)} />}

        <>
          <div>
            <Typography variant="h4" className="mb-2">
              {t('verifyAccount', 'Xác thực tài khoản')}
            </Typography>
            <Typography variant="body2" color="muted" className="mb-2">
              {registerInfo?.maskedEmail
                ? t(
                    'verifyEmailDescription',
                    'Chúng tôi đã gửi mã xác thực đến email {{email}}. Vui lòng kiểm tra hộp thư đến của bạn.',
                    { email: registerInfo.maskedEmail }
                  )
                : t(
                    'verifyDescription',
                    'Chúng tôi đã gửi mã xác thực. Vui lòng kiểm tra email hoặc tin nhắn của bạn.'
                  )}
            </Typography>

            {/* Hiển thị đồng hồ đếm ngược */}
            {registerInfo?.expiresAt ? (
              <CountdownTimer
                expiresAt={registerInfo.expiresAt}
                onExpire={() => setIsOtpExpired(true)}
                className="mb-4"
              />
            ) : (
              <></>
            )}
          </div>

          <div className="space-y-6">
            <div>
              <OTPInput
                length={6}
                onChange={setOtp}
                onEnterPress={handleVerifyOtp}
                autoFocus
                className="mb-4"
                disabled={isOtpExpired}
              />
            </div>

            {isOtpExpired ? (
              <Alert
                type="warning"
                message={t('otpExpiredMessage', 'Mã OTP đã hết hạn. Vui lòng gửi lại mã mới.')}
                className="mb-4"
              />
            ) : null}

            <Button
              type="button"
              variant="primary"
              fullWidth
              onClick={() => handleVerifyOtp()}
              isLoading={verifyCompanyEmail.isPending}
              disabled={otp.length !== 6 || isOtpExpired}
            >
              {t('verify', 'Xác thực')}
            </Button>

            <div className="flex flex-col space-y-4 mt-4">
              <Link to="/auth">
                <Button variant="ghost" fullWidth>
                  {t('backToLogin', 'Quay lại đăng nhập')}
                </Button>
              </Link>
            </div>
          </div>
        </>
      </div>
    </Card>
  );
};

export default VerifyEmailPage;
