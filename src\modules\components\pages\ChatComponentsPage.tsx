import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import ChatInterface, { ChatMessage } from '@/shared/components/chat/ChatInterface';
import { Container, Typography } from '@/shared/components/common';

import ComponentDemo from '../components/ComponentDemo';

/**
 * Trang hiển thị các chat components
 */
const ChatComponentsPage: React.FC = () => {
  const { t } = useTranslation();

  // Người dùng hiện tại
  const currentUser = {
    id: 'user-1',
    name: '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
    avatar:
      'https://images.unsplash.com/photo-1599566150163-29194dcaad36?q=80&w=100&auto=format&fit=crop',
  };

  // Bot
  const bot = {
    id: 'bot-1',
    name: 'AI Assistant',
    avatar:
      'https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=100&auto=format&fit=crop',
    isBot: true,
  };

  // Dữ liệu mẫu cho ChatInterface
  const initialMessages: ChatMessage[] = [
    {
      id: '1',
      content: 'Xin chào! Tôi có thể giúp gì cho bạn?',
      type: 'text',
      sender: bot,
      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      isRead: true,
    },
    {
      id: '2',
      content: 'Tôi cần thông tin về sản phẩm mới',
      type: 'text',
      sender: currentUser,
      timestamp: new Date(Date.now() - 55 * 60 * 1000).toISOString(),
      isRead: true,
      isSent: true,
    },
    {
      id: '3',
      content:
        'Chúng tôi vừa ra mắt sản phẩm mới vào tuần trước. Bạn có thể xem thông tin chi tiết tại đây.',
      type: 'text',
      sender: bot,
      timestamp: new Date(Date.now() - 50 * 60 * 1000).toISOString(),
      isRead: true,
    },
    {
      id: '4',
      content: 'Đây là hình ảnh sản phẩm mới của chúng tôi',
      type: 'image',
      sender: bot,
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      isRead: true,
      imageUrl:
        'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=1000&auto=format&fit=crop',
    },
    {
      id: '5',
      content: 'Cảm ơn bạn đã chia sẻ thông tin',
      type: 'text',
      sender: currentUser,
      timestamp: new Date(Date.now() - 40 * 60 * 1000).toISOString(),
      isRead: true,
      isSent: true,
    },
    {
      id: '6',
      content: 'Đây là bảng giá sản phẩm mới',
      type: 'file',
      sender: bot,
      timestamp: new Date(Date.now() - 35 * 60 * 1000).toISOString(),
      isRead: true,
      file: {
        name: 'bang-gia-san-pham.pdf',
        size: 1024 * 1024 * 2.5,
        type: 'application/pdf',
        url: '#',
      },
    },
    {
      id: '7',
      content: 'Người dùng đã tham gia cuộc trò chuyện',
      type: 'system',
      sender: {
        id: 'system',
        name: 'System',
      },
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    },
  ];

  // State cho tin nhắn
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [isAiThinking, setIsAiThinking] = useState(false);

  // Xử lý khi gửi tin nhắn
  const handleSendMessage = (content: string) => {
    // Thêm tin nhắn của người dùng
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content,
      type: 'text',
      sender: currentUser,
      timestamp: new Date().toISOString(),
      isSent: true,
      isRead: false,
    };

    setMessages(prev => [...prev, userMessage]);

    // Giả lập AI đang suy nghĩ
    setIsAiThinking(true);

    // Giả lập phản hồi từ AI sau 2 giây
    setTimeout(() => {
      setIsAiThinking(false);

      const botMessage: ChatMessage = {
        id: `bot-${Date.now()}`,
        content: `Cảm ơn bạn đã liên hệ. Tôi đã nhận được tin nhắn: "${content}"`,
        type: 'text',
        sender: bot,
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      setMessages(prev => [...prev, botMessage]);
    }, 2000);
  };

  // Xử lý khi gửi hình ảnh
  const handleSendImage = (file: File) => {
    // Giả lập URL hình ảnh
    const imageUrl = URL.createObjectURL(file);

    // Thêm tin nhắn hình ảnh
    const imageMessage: ChatMessage = {
      id: `image-${Date.now()}`,
      content: 'Đã gửi hình ảnh',
      type: 'image',
      sender: currentUser,
      timestamp: new Date().toISOString(),
      isSent: true,
      isRead: false,
      imageUrl,
    };

    setMessages(prev => [...prev, imageMessage]);
  };

  // Xử lý khi gửi file
  const handleSendFile = (file: File) => {
    // Thêm tin nhắn file
    const fileMessage: ChatMessage = {
      id: `file-${Date.now()}`,
      content: 'Đã gửi file',
      type: 'file',
      sender: currentUser,
      timestamp: new Date().toISOString(),
      isSent: true,
      isRead: false,
      file: {
        name: file.name,
        size: file.size,
        type: file.type,
        url: '#',
      },
    };

    setMessages(prev => [...prev, fileMessage]);
  };

  // Xử lý khi tải thêm tin nhắn cũ
  const handleLoadMoreMessages = () => {
    // Giả lập tải thêm tin nhắn cũ
    console.log('Loading more messages...');
  };

  return (
    <Container>
      <Typography variant="h2" className="mb-6">
        {t('components.chat.title', 'Chat Components')}
      </Typography>

      <Typography variant="body1" className="mb-8">
        {t(
          'components.chat.description',
          'Các components hỗ trợ tính năng chat và tương tác với người dùng.'
        )}
      </Typography>

      {/* ChatInterface */}
      <ComponentDemo
        title={t('components.chat.chatInterface.title', 'Chat Interface')}
        description={t(
          'components.chat.chatInterface.description',
          'Giao diện chat với khách hàng hỗ trợ nhiều loại tin nhắn và tương tác.'
        )}
        code={`import { ChatInterface } from '@/shared/components/chat';

<ChatInterface
  title="Hỗ trợ khách hàng"
  description="Kênh hỗ trợ khách hàng trực tuyến"
  messages={messages}
  currentUser={{
    id: 'user-1',
    name: 'Nguyễn Văn A',
    avatar: 'https://example.com/avatar.jpg'
  }}
  onSendMessage={handleSendMessage}
  onSendImage={handleSendImage}
  onSendFile={handleSendFile}
  onLoadMoreMessages={handleLoadMoreMessages}
  isAiThinking={isAiThinking}
  showToolbar={true}
  showImageButton={true}
  showFileButton={true}
  height={500}
/>`}
      >
        <div className="w-full max-w-2xl">
          <ChatInterface
            title="Hỗ trợ khách hàng"
            description="Kênh hỗ trợ khách hàng trực tuyến"
            messages={messages}
            currentUser={currentUser}
            onSendMessage={handleSendMessage}
            onSendImage={handleSendImage}
            onSendFile={handleSendFile}
            onLoadMoreMessages={handleLoadMoreMessages}
            isAiThinking={isAiThinking}
            showToolbar={true}
            showImageButton={true}
            showFileButton={true}
            height={500}
          />
        </div>
      </ComponentDemo>
    </Container>
  );
};

export default ChatComponentsPage;
