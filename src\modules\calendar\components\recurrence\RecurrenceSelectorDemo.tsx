import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button, DatePicker } from '@/shared/components/common';

import { RecurrenceOptions } from './recurrence.types';
import RecurrenceSelector from './RecurrenceSelector';

/**
 * Component demo cho RecurrenceSelector
 */
const RecurrenceSelectorDemo: React.FC = () => {
  const { t } = useTranslation();

  // State cho ngày bắt đầu sự kiện
  const [eventStartDate, setEventStartDate] = useState<Date>(new Date());

  // State cho cấu hình lặp lại
  const [recurrence, setRecurrence] = useState<RecurrenceOptions | null>(null);

  // Reset lựa chọn
  const handleReset = () => {
    setRecurrence(null);
  };

  return (
    <div className="p-6">
      <Typography variant="h1" className="mb-6">
        {t('calendar.recurrenceSelectorDemo', 'Demo RecurrenceSelector')}
      </Typography>

      <Card className="p-4 mb-6">
        <Typography variant="h3" className="mb-4">
          {t('calendar.eventStartDate', 'Ngày bắt đầu sự kiện')}
        </Typography>

        <div className="max-w-xs">
          <DatePicker
            label={t('calendar.startDate', 'Ngày bắt đầu')}
            value={eventStartDate}
            onChange={date => date && setEventStartDate(date)}
            format="dd/MM/yyyy"
            fullWidth
          />
        </div>
      </Card>

      <Card className="p-4">
        <Typography variant="h3" className="mb-4">
          {t('calendar.recurrenceOptions', 'Tùy chọn lặp lại')}
        </Typography>

        <RecurrenceSelector
          value={recurrence}
          onChange={setRecurrence}
          eventStartDate={eventStartDate}
          label={t('calendar.recurrence', 'Lặp lại')}
        />

        {recurrence && (
          <div className="mt-6 p-3 bg-card-muted rounded-md">
            <Typography variant="h4" className="mb-2">
              {t('calendar.recurrenceConfig', 'Cấu hình lặp lại')}:
            </Typography>
            <pre className="text-sm overflow-auto">{JSON.stringify(recurrence, null, 2)}</pre>
          </div>
        )}

        <div className="mt-6 flex justify-center">
          <Button variant="outline" onClick={handleReset} className="px-4 py-2">
            {t('common.reset', 'Đặt lại')}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default RecurrenceSelectorDemo;
