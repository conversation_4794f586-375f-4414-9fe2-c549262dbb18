import React from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Icon, Toggle, FormGrid, Select } from '@/shared/components/common';
import { useIsMobile } from '@/shared/hooks/common';
import { cn } from '@/shared/utils/cn';

export interface CalendarToolbarProps {
  /**
   * Hiển thị ngày cuối tuần
   */
  weekends: boolean;

  /**
   * Callback khi thay đổi hiển thị ngày cuối tuần
   */
  onWeekendsToggle: (show: boolean) => void;

  /**
   * Danh sách các loại sự kiện để lọc
   */
  eventTypes?: Array<{
    value: string;
    label: string;
    color: string;
  }>;

  /**
   * Các loại sự kiện đang được chọn để lọc
   */
  selectedEventTypes?: string[];

  /**
   * Callback khi thay đổi lọc loại sự kiện
   */
  onEventTypeFilter?: (types: string[]) => void;

  /**
   * CSS class bổ sung
   */
  className?: string;
}

/**
 * Component thanh công cụ cho calendar với các tùy chọn lọc và hiển thị
 *
 * @example
 * ```tsx
 * <CalendarToolbar
 *   weekends={showWeekends}
 *   onWeekendsToggle={setShowWeekends}
 *   eventTypes={eventTypes}
 *   selectedEventTypes={selectedTypes}
 *   onEventTypeFilter={handleFilterChange}
 * />
 * ```
 */
const CalendarToolbar: React.FC<CalendarToolbarProps> = ({
  weekends,
  onWeekendsToggle,
  eventTypes = [],
  selectedEventTypes = [],
  onEventTypeFilter,
  className,
}) => {
  const { t } = useTranslation();
  const isMobile = useIsMobile();

  // Tạo options cho Select component
  const eventTypeOptions = eventTypes.map(type => ({
    value: type.value,
    label: type.label,
  }));

  return (
    <div
      className={cn('calendar-toolbar p-3 bg-card border border-border rounded-lg mb-4', className)}
    >
      <FormGrid columns={isMobile ? 1 : 3} gap="md">
        {/* Toggle hiển thị ngày cuối tuần */}
        <div className="flex items-center gap-2">
          <Toggle checked={weekends} onChange={onWeekendsToggle} size="sm" />
          <span className="text-sm">{t('calendar.showWeekends', 'Hiển thị cuối tuần')}</span>
        </div>

        {/* Lọc theo loại sự kiện */}
        {eventTypes.length > 0 && onEventTypeFilter && (
          <div className="flex-1">
            <Select
              options={eventTypeOptions}
              value={selectedEventTypes}
              onChange={values => onEventTypeFilter(values as string[])}
              placeholder={t('calendar.filterByType', 'Lọc theo loại')}
              multiple
              searchable
              fullWidth
            />
          </div>
        )}

        {/* Các nút tác vụ khác */}
        <div className="flex justify-end gap-2">
          <Button variant="outline" size="sm" leftIcon={<Icon name="filter" size="sm" />}>
            {t('calendar.moreFilters', 'Lọc khác')}
          </Button>

          <Button variant="outline" size="sm" leftIcon={<Icon name="save" size="sm" />}>
            {t('calendar.export', 'Xuất')}
          </Button>
        </div>
      </FormGrid>
    </div>
  );
};

export default CalendarToolbar;
