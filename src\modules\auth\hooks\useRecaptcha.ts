import React, { useCallback, useEffect, useState } from 'react';

import { env } from '@/shared/utils';

import { GrecaptchaEnterprise } from '../types/recaptcha.types';

/**
 * Hook để quản lý reCAPTCHA
 * @param containerId ID của container để render reCAPTCHA
 * @param action Hành động của reCAPTCHA (LOGIN, REGISTER, ...)
 */
export const useRecaptcha = (containerId: string, action: string) => {
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Biến để theo dõi trạng thái khởi tạo
  const recaptchaRef = React.useRef<{
    initialized: boolean;
    id: number | null;
    timeoutId: NodeJS.Timeout | null;
    checkVisibilityInterval: NodeJS.Timeout | null;
    initFunction: (() => void) | null;
  }>({
    initialized: false,
    id: null,
    timeoutId: null,
    checkVisibilityInterval: null,
    initFunction: null,
  });

  // Tạo một biến mutable để lưu trữ hàm khởi tạo
  const initRecaptchaRef = React.useRef<() => void>();

  // Khởi tạo reCAPTCHA
  useEffect(() => {
    // Hàm để tải lại script reCAPTCHA nếu cần
    const reloadRecaptchaScript = () => {
      // Xóa script cũ nếu có
      const oldScript = document.querySelector('script[src*="recaptcha/enterprise.js"]');
      if (oldScript) {
        oldScript.remove();
      }

      // Tạo script mới
      const script = document.createElement('script');
      script.src = 'https://www.google.com/recaptcha/enterprise.js?render=explicit';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        // Khởi tạo lại reCAPTCHA sau khi script được tải
        setTimeout(initRecaptcha, 500);
      };
      script.onerror = () => {
        setError('Không thể tải script reCAPTCHA, vui lòng thử lại');
      };

      document.head.appendChild(script);
    };

    const initRecaptcha = () => {
      // Lưu hàm khởi tạo vào ref để có thể gọi lại từ bên ngoài useEffect
      initRecaptchaRef.current = initRecaptcha;

      // Nếu đã khởi tạo rồi thì không khởi tạo lại
      if (recaptchaRef.current.initialized) {
        return;
      }

      const win = window as Window &
        typeof globalThis & { grecaptcha?: { enterprise: GrecaptchaEnterprise } };
      if (win.grecaptcha?.enterprise) {
        win.grecaptcha.enterprise.ready(() => {
          try {
            // Kiểm tra xem container cha có tồn tại không
            const parentContainer = document.getElementById(containerId);
            if (!parentContainer) {
              console.warn(`reCAPTCHA parent container ${containerId} not found`);
              setError(`Container ${containerId} not found`);
              return;
            }

            // Xóa tất cả nội dung trong container cha
            parentContainer.innerHTML = '';

            // Tạo một container mới cho reCAPTCHA
            const newContainerId = `${containerId}-inner-${Date.now()}`;
            const newContainer = document.createElement('div');
            newContainer.id = newContainerId;
            newContainer.style.display = 'flex';
            newContainer.style.justifyContent = 'center';
            newContainer.style.width = '100%';

            // Thêm container mới vào container cha
            parentContainer.appendChild(newContainer);

            // Đánh dấu là đã khởi tạo trước khi render để tránh render nhiều lần
            recaptchaRef.current.initialized = true;

            // Render reCAPTCHA mới vào container mới
            const id = win.grecaptcha!.enterprise.render(newContainerId, {
              sitekey: env.recaptchaSiteKey,
              callback: (token: string) => {
                if (!token) {
                  // Token hết hạn hoặc lỗi
                  console.log(`reCAPTCHA for ${action} expired or error, resetting token`);
                  setRecaptchaToken(null);
                  setError('reCAPTCHA đã hết hạn hoặc gặp lỗi, vui lòng xác thực lại');
                } else {
                  // Token hợp lệ
                  setRecaptchaToken(token);
                  setIsLoaded(true);
                  setError(null);
                  console.log(
                    `reCAPTCHA token received for ${action}:`,
                    `${token.substring(0, 10)  }...`
                  );
                }
              },
              // Các tùy chọn khác được xử lý bởi grecaptcha
            });
            recaptchaRef.current.id = id;

            // Kiểm tra xem reCAPTCHA có hiển thị đúng không sau khi render
            // Giới hạn số lần kiểm tra để tránh log liên tục
            let checkCount = 0;
            const MAX_CHECKS = 10; // Tối đa 10 lần kiểm tra (5 giây)

            // Nếu đã có interval đang chạy, xóa nó trước khi tạo mới
            if (recaptchaRef.current.checkVisibilityInterval) {
              clearInterval(recaptchaRef.current.checkVisibilityInterval);
              recaptchaRef.current.checkVisibilityInterval = null;
            }

            // Tạo một biến local để theo dõi interval ID
            // Điều này giúp tránh việc sử dụng recaptchaRef.current có thể thay đổi
            let localIntervalId: NodeJS.Timeout | null = null;

            localIntervalId = setInterval(() => {
              checkCount++;
              const parentContainer = document.getElementById(containerId);
              const newContainer = document.getElementById(newContainerId);

              // Nếu đã kiểm tra quá số lần tối đa, dừng interval
              if (checkCount >= MAX_CHECKS) {
                if (localIntervalId) {
                  clearInterval(localIntervalId);
                  localIntervalId = null;

                  // Cũng xóa trong ref để đảm bảo không có interval nào còn chạy
                  if (recaptchaRef.current.checkVisibilityInterval) {
                    clearInterval(recaptchaRef.current.checkVisibilityInterval);
                    recaptchaRef.current.checkVisibilityInterval = null;
                  }
                }

                // Chỉ log một lần và không thử lại nữa
                // Để tránh vòng lặp vô hạn của việc tạo interval mới
                if (parentContainer && (!newContainer || !newContainer.querySelector('iframe'))) {
                  setError('Không thể tải reCAPTCHA, vui lòng thử lại sau');
                }
                return;
              }

              if (parentContainer && newContainer) {
                const iframe = newContainer.querySelector('iframe');
                if (iframe) {
                  // Tìm thấy iframe, dừng kiểm tra
                  if (localIntervalId) {
                    clearInterval(localIntervalId);
                    localIntervalId = null;
                  }

                  // Cũng xóa trong ref để đảm bảo không có interval nào còn chạy
                  if (recaptchaRef.current.checkVisibilityInterval) {
                    clearInterval(recaptchaRef.current.checkVisibilityInterval);
                    recaptchaRef.current.checkVisibilityInterval = null;
                  }

                  // Kiểm tra kích thước iframe
                  const { width, height } = iframe.getBoundingClientRect();

                  if (width < 10 || height < 10) {
                    // Không thử render lại để tránh vòng lặp vô hạn
                    setError('reCAPTCHA hiển thị không đúng, vui lòng thử lại sau');
                  } else {
                    // Đánh dấu là đã tải thành công
                    setIsLoaded(true);
                    setError(null);
                  }
                }
              }
              // Không log gì cả để tránh spam console
            }, 500);

            // Lưu interval ID vào ref để có thể xóa khi cần
            recaptchaRef.current.checkVisibilityInterval = localIntervalId;
          } catch (error) {
            console.error(`Error rendering reCAPTCHA for ${action}:`, error);
            setError(
              `Error rendering reCAPTCHA: ${error instanceof Error ? error.message : String(error)}`
            );
            // Nếu có lỗi, đánh dấu là chưa khởi tạo để có thể thử lại
            recaptchaRef.current.initialized = false;
          }
        });
      } else {
        // Kiểm tra xem script đã được tải chưa
        const recaptchaScript = document.querySelector('script[src*="recaptcha/enterprise.js"]');
        if (!recaptchaScript) {
          reloadRecaptchaScript();
        } else {
          // Thử lại sau 500ms nếu grecaptcha chưa sẵn sàng
          recaptchaRef.current.timeoutId = setTimeout(initRecaptcha, 500);

          // Nếu sau 5 giây vẫn không tải được, thử tải lại script
          setTimeout(() => {
            const win = window as Window &
              typeof globalThis & { grecaptcha?: { enterprise: GrecaptchaEnterprise } };
            if (!win.grecaptcha?.enterprise) {
              reloadRecaptchaScript();
            }
          }, 5000);
        }
      }
    };

    // Khởi tạo reCAPTCHA
    initRecaptcha();

    // Lưu trữ các giá trị cần thiết từ ref để sử dụng trong cleanup function
    // Điều này giúp tránh cảnh báo exhaustive-deps
    const currentTimeoutId = recaptchaRef.current.timeoutId;
    const currentIntervalId = recaptchaRef.current.checkVisibilityInterval;
    const currentRecaptchaId = recaptchaRef.current.id;

    // Cleanup function khi component unmount
    return () => {
      try {
        // Xóa tất cả các timeout toàn cục có thể liên quan đến reCAPTCHA
        const win = window as Window & typeof globalThis;
        for (let i = 1; i < 1000; i++) {
          win.clearTimeout(i);
          win.clearInterval(i);
        }

        // Xóa timeout đã lưu
        if (currentTimeoutId) {
          clearTimeout(currentTimeoutId);
        }

        // Xóa interval đã lưu
        if (currentIntervalId) {
          clearInterval(currentIntervalId);
        }

        // Reset reCAPTCHA nếu có ID
        if (currentRecaptchaId !== null && currentRecaptchaId !== undefined) {
          const win = window as Window &
            typeof globalThis & { grecaptcha?: { enterprise: GrecaptchaEnterprise } };
          if (win.grecaptcha?.enterprise) {
            try {
              win.grecaptcha.enterprise.reset(currentRecaptchaId);
            } catch {
              // Lỗi khi reset reCAPTCHA không quan trọng khi unmount
            }
          }
        }

        // Xóa container
        const parentContainer = document.getElementById(containerId);
        if (parentContainer) {
          parentContainer.innerHTML = '';
        }
      } catch {
        // Bỏ qua lỗi trong cleanup function
      }
    };
  }, [containerId, action]);

  // Hàm reset reCAPTCHA
  const resetRecaptcha = useCallback(() => {
    try {
      // Reset state
      setRecaptchaToken(null);
      setIsLoaded(false);
      setError(null);

      // Xóa tất cả các interval và timeout đang chạy
      if (recaptchaRef.current) {
        // Xóa interval kiểm tra visibility
        if (recaptchaRef.current.checkVisibilityInterval) {
          clearInterval(recaptchaRef.current.checkVisibilityInterval);
          recaptchaRef.current.checkVisibilityInterval = null;
        }

        // Xóa timeout
        if (recaptchaRef.current.timeoutId) {
          clearTimeout(recaptchaRef.current.timeoutId);
          recaptchaRef.current.timeoutId = null;
        }

        // Đánh dấu là chưa khởi tạo để có thể khởi tạo lại
        recaptchaRef.current.initialized = false;
        recaptchaRef.current.id = null;
      }

      // Xóa container cũ
      const parentContainer = document.getElementById(containerId);
      if (parentContainer) {
        parentContainer.innerHTML = '';

        // Đợi một chút trước khi khởi tạo lại để đảm bảo DOM đã được cập nhật
        setTimeout(() => {
          if (initRecaptchaRef.current) {
            initRecaptchaRef.current();
          }
        }, 300); // Tăng thời gian chờ lên 300ms để đảm bảo DOM đã được cập nhật
      } else {
        setError(`Container ${containerId} not found for reset`);
      }
    } catch (error) {
      setError(
        `Error resetting reCAPTCHA: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }, [containerId]); // action không được sử dụng trong callback

  return {
    recaptchaToken,
    isLoaded,
    error,
    resetRecaptcha,
  };
};
