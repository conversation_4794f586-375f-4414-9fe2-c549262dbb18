{"components": {"calendar": {"title": "Calendar Components", "description": "Collection of calendar and event management components", "basic": {"title": "Basic Calendar", "description": "Basic calendar with event display, date selection, and interaction", "example": {"title": "Basic Calendar", "description": "Calendar with sample events, date selection, and event click"}, "usage": {"title": "Usage", "description": "How to use the Calendar component"}}, "eventForm": {"title": "Event Form", "description": "Form for adding and editing calendar events with various options", "example": {"title": "Event Form", "description": "Add/edit event form with basic and advanced fields"}, "usage": {"title": "Usage", "description": "How to use the EventForm component"}, "showForm": "Show event form", "addEvent": "Add event"}, "recurrence": {"title": "Recurrence Selector", "description": "Component for configuring event recurrence with various options", "basic": {"title": "Basic Recurrence Configuration", "description": "Recurrence configuration component with null initial value"}, "daily": {"title": "Daily Recurrence", "description": "Daily recurrence configuration component"}, "weekly": {"title": "Weekly Recurrence", "description": "Weekly recurrence configuration with days of the week"}, "usage": {"title": "Usage", "description": "How to use the RecurrenceSelector component"}, "label": "Repeat", "dailyLabel": "Daily repeat", "weeklyLabel": "Weekly repeat", "currentValue": "Current value"}, "reminders": {"title": "Reminder Selector", "description": "Component for configuring event reminders", "basic": {"title": "Basic Reminder Configuration", "description": "Reminder configuration component with default values"}, "empty": {"title": "Empty Reminder Configuration", "description": "Reminder configuration component with empty list"}, "disabled": {"title": "Disabled Re<PERSON>er Configuration", "description": "Reminder configuration component in disabled state"}, "usage": {"title": "Usage", "description": "How to use the ReminderSelector component"}, "label": "Reminders", "disabledLabel": "Reminders (Disabled)", "currentValue": "Current value"}, "userSelector": {"title": "User Selector", "description": "Component for selecting users for events", "basic": {"title": "Basic User Selection", "description": "Component for selecting a single user"}, "multiple": {"title": "Multiple User Selection", "description": "Component for selecting multiple users"}, "groups": {"title": "User Selection with Groups", "description": "Component for selecting users with user groups"}, "usage": {"title": "Usage", "description": "How to use the UserSelector component"}, "label": "Participants", "multipleLabel": "Participants", "groupsLabel": "Participants", "currentValue": "Current value"}, "fileUploader": {"title": "File Uploader", "description": "Component for uploading attachment files for events", "basic": {"title": "Basic File Upload", "description": "File upload component with sample files"}, "empty": {"title": "Empty File Upload", "description": "File upload component with empty list"}, "restricted": {"title": "File Upload with Type Restrictions", "description": "File upload component with file type restrictions"}, "usage": {"title": "Usage", "description": "How to use the FileUploader component"}, "label": "Attachments", "emptyLabel": "Attachments", "imageLabel": "Upload images", "currentValue": "Current value"}}, "flowNodes": {"title": "Flow Nodes & Edges", "description": "Custom node and edge types for React Flow, supporting the creation of diagrams, organization charts, and workflows.", "examples": "Examples", "selectExample": "Select Example", "nodeTypes": "Node Types", "edgeTypes": "Edge Types"}, "kanban": {"title": "Kanban Board", "description": "Drag & drop kanban board for task management with columns and cards", "demo": "Demo Kanban Board", "usage": "Usage", "usageDescription": "To use the Kanban Board, you need to import the component and provide initial data:", "props": "Props"}, "form": {"wizard": {"title": "Form Wizard", "description": "FormWizard component manages multi-step forms with validation and navigation.", "basic": {"title": "Basic Form Wizard", "description": "Basic form wizard with 3 steps and validation."}}}, "charts": {"demo": {"title": "Chart Demo", "description": "Chart components with responsive design, multilingual support, and theme compatibility."}, "lineChart": {"title": "Line Chart", "description": "LineChart component displays data as lines, supporting multiple data lines, tooltips, and legends.", "basic": {"title": "Basic Line Chart", "description": "Basic line chart with a single data line."}, "multiLine": {"title": "Multi-line Chart", "description": "Line chart with multiple data lines."}, "customized": {"title": "Customized Line Chart", "description": "Line chart with customizations like line type, stroke width, and dot display."}}}, "animation": {"title": "Animation", "description": "Dynamic animations available in RedAI Frontend Template."}, "notification": {"title": "Notification", "description": "Display notifications to users about the status of actions or important information.", "basic": {"title": "Basic Notification", "description": "Basic notification types with different styles."}, "hook": {"title": "Using useNotification Hook", "description": "Manage notifications with the useNotification hook."}, "hideCode": "Hide code", "showCode": "Show code"}, "banner": {"title": "Banner", "description": "Banner component displays highlighted content with various options.", "basic": {"title": "Basic Banner", "description": "Basic banner with title and description."}, "withBackground": {"title": "Banner with Background", "description": "Banner with background image and overlay."}, "gradient": {"title": "Banner with <PERSON><PERSON><PERSON>", "description": "Banner with gradient background and action buttons."}, "wave": {"title": "Banner with Wave Effect", "description": "Banner with wave effect at the bottom."}, "custom": {"title": "Banner with Custom Content", "description": "Banner with custom content instead of using title and description.", "exploreButton": "Explore Now"}, "borderRadius": {"title": "Banner with Border Radius", "description": "Banner with different border radius options.", "topCorners": {"title": "Banner with Top Corners Radius", "description": "Using borderRadius='rounded-t-xl' property"}, "allCorners": {"title": "Banner with All Corners Radius", "description": "Using borderRadius='rounded-xl' property"}, "bottomCorners": {"title": "Banner with Bottom Corners Radius", "description": "Using borderRadius='rounded-b-xl' property"}}}, "categories": {"buttons": {"title": "Buttons", "description": "Different types of buttons: primary, secondary, outline, icon buttons..."}, "cards": {"title": "Cards", "description": "Various card types for displaying content, information, data..."}, "chips": {"title": "Chips", "description": "Chips are compact elements that represent an input, attribute, or action..."}, "inputs": {"title": "Inputs", "description": "Various input types: text, number, checkbox, radio, select..."}, "layout": {"title": "Layout Components", "description": "Layout components: container, grid, flex, resizer..."}, "theme": {"title": "Theme Components", "description": "Theme-related components: theme toggle, language selector...", "system": {"title": "Theme System", "description": "New theme system with customization and extension capabilities"}}, "form": {"title": "Form Components", "description": "Form components: input, select, checkbox, radio...", "theme": {"title": "Form with Theme System", "description": "Demo of form components using the new theme system"}}, "typography": {"title": "Typography", "description": "Text formatting components for consistent display in applications."}}, "grid": {"title": "Grid", "description": "Grid component helps create flexible and responsive grid layouts."}, "responsiveGrid": {"title": "Responsive Grid", "description": "Advanced responsive grid that automatically adjusts based on screen size and chat panel state."}, "menu": {"title": "<PERSON><PERSON>", "description": "Menu with multiple features: submenu, different modes, collapsed state"}, "tooltip": {"title": "<PERSON><PERSON><PERSON>", "description": "Tooltip displays additional information when hovering over an element"}, "searchBar": {"title": "Search Bar", "description": "Search bar with animation effects and different styles"}, "modernMenu": {"title": "Modern Menu", "description": "Modern menu with various styles and positions"}, "cards": {"title": "Cards", "description": "Various card types for displaying content, information, and data in the RedAI system."}, "avatar": {"title": "Avatar", "description": "Avatar component displays user profile images."}, "imageGallery": {"title": "Image Gallery", "description": "Component for displaying image collections with various options."}, "topCard": {"title": "Top Card", "description": "Component for displaying overview information in card format."}, "simpleChart": {"title": "Simple Chart", "description": "Simple chart using the Recharts library directly"}, "deleteConfirmModal": {"title": "Delete Confirmation Modal", "description": "Reusable modal for confirming delete actions", "basic": {"title": "Basic", "description": "Basic delete confirmation modal with default messages", "button": "Delete Item"}, "custom": {"title": "Custom", "description": "Delete confirmation modal with custom content and item name", "button": "Delete User", "modalTitle": "Delete User", "modalDescription": "This action cannot be undone. All data associated with this user will be permanently deleted."}, "loading": {"title": "With Loading State", "description": "Delete confirmation modal with loading state while processing", "button": "Delete with Loading"}}, "product": {"title": "Product Components", "description": "Components for product management including forms and filters", "forms": {"title": "Product Forms", "description": "Forms for creating and editing products and segments", "productForm": {"title": "Product Form", "description": "Form for creating and editing products"}, "segmentForm": {"title": "Segment Form", "description": "Form for creating and editing product segments"}}, "filters": {"title": "Product Filters", "description": "Filter components for products"}, "cards": {"title": "Product Cards", "description": "Various card layouts for displaying products"}, "pages": {"title": "Product Pages", "description": "Demo pages for product management module", "list": {"title": "Product List Page", "description": "Page for displaying and managing products"}, "detail": {"title": "Product Detail Page", "description": "Page for viewing and editing product details"}, "segment": {"title": "Segment List Page", "description": "Page for managing product segments"}}}}}