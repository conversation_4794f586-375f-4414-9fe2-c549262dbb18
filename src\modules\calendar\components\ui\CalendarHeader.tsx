import React from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON>ton, Icon, Typography, Hidden } from '@/shared/components/common';

import ViewSelect from '../ViewSelect';

export interface CalendarHeaderProps {
  /**
   * Tiêu đề hiển thị (thường là tháng/năm hiện tại)
   */
  title: string;

  /**
   * Chế độ xem hiện tại (dayGridMonth, timeGridWeek, timeGridDay, listWeek)
   */
  currentView: string;

  /**
   * Callback khi nhấn nút prev
   */
  onPrev: () => void;

  /**
   * Callback khi nhấn nút next
   */
  onNext: () => void;

  /**
   * Callback khi nhấn nút today
   */
  onToday: () => void;

  /**
   * Callback khi thay đổi chế độ xem
   */
  onViewChange: (view: string) => void;

  /**
   * Callback khi nhấn nút thêm sự kiện
   */
  onAddEvent: () => void;
}

/**
 * Component header cho calendar với các nút điều hướng và chọn chế độ xem
 *
 * @example
 * ```tsx
 * <CalendarHeader
 *   title="Tháng 7 2023"
 *   currentView="dayGridMonth"
 *   onPrev={() => calendarRef.current?.getApi().prev()}
 *   onNext={() => calendarRef.current?.getApi().next()}
 *   onToday={() => calendarRef.current?.getApi().today()}
 *   onViewChange={(view) => handleViewChange(view)}
 *   onAddEvent={handleAddEvent}
 * />
 * ```
 */
const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  title,
  currentView,
  onPrev,
  onNext,
  onToday,
  onViewChange,
  onAddEvent,
}) => {
  const { t } = useTranslation();

  return (
    <div className="calendar-custom-header">
      <div className="calendar-custom-header-left">
        <Button
          variant="outline"
          size="sm"
          className="calendar-nav-button calendar-prev-button"
          onClick={onPrev}
        >
          <Icon name="chevron-left" size="sm" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="calendar-nav-button calendar-next-button"
          onClick={onNext}
        >
          <Icon name="chevron-right" size="sm" />
        </Button>
        <Button variant="outline" size="sm" className="calendar-today-button" onClick={onToday}>
          {t('calendar.today', 'Hôm nay')}
        </Button>
      </div>
      <div className="calendar-custom-header-center">
        <Typography variant="h5" className="calendar-title">
          {title}
        </Typography>
      </div>
      <div className="calendar-custom-header-right">
        <ViewSelect currentView={currentView} onViewChange={onViewChange} />
        <Button
          variant="primary"
          className="calendar-add-event-button"
          leftIcon={<Icon name="plus" size="sm" />}
          onClick={onAddEvent}
        >
          <Hidden hideOnMobile>
            <span>{t('calendar.addEvent', 'Thêm sự kiện')}</span>
          </Hidden>
        </Button>
      </div>
    </div>
  );
};

export default CalendarHeader;
