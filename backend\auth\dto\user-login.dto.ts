import { IsNotEmpty, IsString, MinLength } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho đăng nhập người dùng
 */
export class UserLoginDto {
  @ApiProperty({
    description: 'Username hoặc email của người dùng',
    example: 'johndoe',
  })
  @IsNotEmpty({ message: 'Username không được để trống' })
  @IsString({ message: 'Username phải là chuỗi' })
  username: string;

  @ApiProperty({
    description: 'Mật khẩu tài khoản',
    example: 'StrongPassword123!',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: '<PERSON>ật khẩu phải là chuỗi' })
  @MinLength(6, { message: '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự' })
  password: string;
}
