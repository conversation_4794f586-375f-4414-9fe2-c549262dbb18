import { useCallback } from 'react';

import { useAppDispatch, useAppSelector, RootState } from '@/shared/store';

import { setCompany, clearCompany, setRegisterInfo, clearRegisterInfo } from '../store';
import { RegisterInfo } from '../store/companySlice';
import { Company } from '../types/company-auth.types';

/**
 * Hook quản lý thông tin công ty
 */
export const useCompany = () => {
  const dispatch = useAppDispatch();
  const companyState = useAppSelector((state: RootState) => state.company);

  /**
   * Lưu thông tin công ty
   */
  const saveCompany = useCallback(
    (company: Company) => {
      dispatch(setCompany(company));
      return Promise.resolve(); // Trả về Promise để có thể await
    },
    [dispatch]
  );

  /**
   * Xóa thông tin công ty
   */
  const resetCompany = useCallback(() => {
    dispatch(clearCompany());
    return Promise.resolve(); // Tr<PERSON> về Promise để có thể await
  }, [dispatch]);

  /**
   * <PERSON>ưu thông tin đăng ký vào Redux
   */
  const saveRegisterInfo = useCallback(
    (info: RegisterInfo) => {
      dispatch(setRegisterInfo(info));
      return Promise.resolve(); // Trả về Promise để có thể await
    },
    [dispatch]
  );

  /**
   * Xóa thông tin đăng ký khỏi Redux
   */
  const clearRegisterData = useCallback(() => {
    dispatch(clearRegisterInfo());
    return Promise.resolve(); // Trả về Promise để có thể await
  }, [dispatch]);

  return {
    // State
    company: companyState.company,
    isLoading: companyState.isLoading,
    error: companyState.error,
    registerInfo: companyState.registerInfo,

    // Actions
    saveCompany,
    resetCompany,
    saveRegisterInfo,
    clearRegisterData,
  };
};
