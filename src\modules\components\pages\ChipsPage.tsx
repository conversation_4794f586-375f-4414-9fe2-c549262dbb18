import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Chip, ChipGroup } from '@/shared/components/common';

import { ComponentDemo } from '../components';

const ChipsPage: React.FC = () => {
  const { t } = useTranslation();

  // State for selected chips example
  const [selectedChips, setSelectedChips] = useState<(string | number)[]>([1]);

  // State for deletable chips example
  const [chips, setChips] = useState([
    { id: 1, label: 'React', variant: 'primary' as const },
    { id: 2, label: 'Vue', variant: 'success' as const },
    { id: 3, label: 'Angular', variant: 'danger' as const },
    { id: 4, label: 'Svelte', variant: 'warning' as const },
  ]);

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-8">
        <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-2">
          {t('components.chips.title')}
        </h1>
        <p className="text-muted">{t('components.chips.description')}</p>
      </div>

      <ComponentDemo
        title={t('components.chips.basic.title')}
        description={t('components.chips.basic.description')}
        code={`import { Chip } from '@/shared/components/common';

// Basic chip
<Chip>Default</Chip>

// Variants
<Chip variant="primary">Primary</Chip>
<Chip variant="success">Success</Chip>
<Chip variant="warning">Warning</Chip>
<Chip variant="danger">Danger</Chip>
<Chip variant="info">Info</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip>Default</Chip>
          <Chip variant="primary">Primary</Chip>
          <Chip variant="success">Success</Chip>
          <Chip variant="warning">Warning</Chip>
          <Chip variant="danger">Danger</Chip>
          <Chip variant="info">Info</Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.outlined.title')}
        description={t('components.chips.outlined.description')}
        code={`import { Chip } from '@/shared/components/common';

// Outlined chips
<Chip outlined>Default</Chip>
<Chip outlined variant="primary">Primary</Chip>
<Chip outlined variant="success">Success</Chip>
<Chip outlined variant="warning">Warning</Chip>
<Chip outlined variant="danger">Danger</Chip>
<Chip outlined variant="info">Info</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip outlined>Default</Chip>
          <Chip outlined variant="primary">
            Primary
          </Chip>
          <Chip outlined variant="success">
            Success
          </Chip>
          <Chip outlined variant="warning">
            Warning
          </Chip>
          <Chip outlined variant="danger">
            Danger
          </Chip>
          <Chip outlined variant="info">
            Info
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.sizes.title')}
        description={t('components.chips.sizes.description')}
        code={`import { Chip } from '@/shared/components/common';

// Small chips
<Chip size="sm">Small</Chip>

// Medium chips (default)
<Chip size="md">Medium</Chip>

// Large chips
<Chip size="lg">Large</Chip>`}
      >
        <div className="flex flex-wrap gap-2 items-center">
          <Chip size="sm">Small</Chip>
          <Chip size="md">Medium</Chip>
          <Chip size="lg">Large</Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.icons.title')}
        description={t('components.chips.icons.description')}
        code={`import { Chip } from '@/shared/components/common';

// Chip with left icon
<Chip leftIconName="user">User</Chip>

// Chip with right icon
<Chip rightIconName="chevron-right">Next</Chip>

// Chip with both icons
<Chip leftIconName="star" rightIconName="close">Favorite</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip leftIconName="user">User</Chip>
          <Chip rightIconName="chevron-right">Next</Chip>
          <Chip leftIconName="star" rightIconName="close">
            Favorite
          </Chip>
          <Chip variant="primary" leftIconName="check">
            Completed
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.closable.title')}
        description={t('components.chips.closable.description')}
        code={`import { Chip } from '@/shared/components/common';

// Closable chip
<Chip
  closable
  onClose={() => console.log('Chip closed')}
>
  Closable
</Chip>

// Closable chip with icon
<Chip
  closable
  leftIconName="user"
  onClose={() => console.log('User chip closed')}
>
  User
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip closable onClose={() => console.log('Chip closed')}>
            Closable
          </Chip>
          <Chip closable variant="primary" onClose={() => console.log('Primary chip closed')}>
            Primary
          </Chip>
          <Chip closable leftIconName="user" onClose={() => console.log('User chip closed')}>
            User
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.clickable.title')}
        description={t('components.chips.clickable.description')}
        code={`import { Chip } from '@/shared/components/common';

// Clickable chip
<Chip
  onClick={() => console.log('Chip clicked')}
>
  Click me
</Chip>

// Clickable chip with icon
<Chip
  onClick={() => console.log('Settings clicked')}
  leftIconName="settings"
>
  Settings
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip onClick={() => console.log('Chip clicked')}>Click me</Chip>
          <Chip variant="primary" onClick={() => console.log('Primary clicked')}>
            Primary
          </Chip>
          <Chip onClick={() => console.log('Settings clicked')} leftIconName="settings">
            Settings
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.disabled.title')}
        description={t('components.chips.disabled.description')}
        code={`import { Chip } from '@/shared/components/common';

// Disabled chip
<Chip disabled>Disabled</Chip>

// Disabled clickable chip
<Chip
  disabled
  onClick={() => console.log('This will not be called')}
>
  Disabled clickable
</Chip>

// Disabled closable chip
<Chip
  disabled
  closable
  onClose={() => console.log('This will not be called')}
>
  Disabled closable
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip disabled>Disabled</Chip>
          <Chip disabled onClick={() => console.log('This will not be called')}>
            Disabled clickable
          </Chip>
          <Chip disabled closable onClose={() => console.log('This will not be called')}>
            Disabled closable
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.avatar.title')}
        description={t('components.chips.avatar.description')}
        code={`import { Chip } from '@/shared/components/common';

// Chip with avatar
<Chip
  avatarSrc="/assets/images/ai-agents/assistant-robot.svg"
>
  Assistant
</Chip>

// Chip with avatar and closable
<Chip
  avatarSrc="/assets/images/ai-agents/coder-robot.svg"
  closable
  onClose={() => console.log('Avatar chip closed')}
>
  Coder
</Chip>

// Chip with avatar and variant
<Chip
  avatarSrc="/assets/images/ai-agents/writer-robot.svg"
  variant="primary"
>
  Writer
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip avatarSrc="/assets/images/ai-agents/assistant-robot.svg">Assistant</Chip>
          <Chip
            avatarSrc="/assets/images/ai-agents/coder-robot.svg"
            closable
            onClose={() => console.log('Avatar chip closed')}
          >
            Coder
          </Chip>
          <Chip avatarSrc="/assets/images/ai-agents/writer-robot.svg" variant="primary">
            Writer
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.loading.title')}
        description={t('components.chips.loading.description')}
        code={`import { Chip } from '@/shared/components/common';

// Loading chip
<Chip loading>Loading</Chip>

// Loading chip with variant
<Chip loading variant="primary">
  Loading Primary
</Chip>

// Loading chip with icon
<Chip loading leftIconName="settings">
  Loading with Icon
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip loading>Loading</Chip>
          <Chip loading variant="primary">
            Loading Primary
          </Chip>
          <Chip loading leftIconName="settings">
            Loading with Icon
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.selected.title')}
        description={t('components.chips.selected.description')}
        code={`import { Chip } from '@/shared/components/common';

// Selected chip
<Chip isSelected>Selected</Chip>

// Selected chip with variant
<Chip isSelected variant="primary">
  Selected Primary
</Chip>

// Selectable chip
<Chip
  isSelected={isSelected}
  onSelect={(selected) => setIsSelected(selected)}
>
  Click to select
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip isSelected>Selected</Chip>
          <Chip isSelected variant="primary">
            Selected Primary
          </Chip>
          <Chip
            isSelected={selectedChips.includes(1)}
            onSelect={selected => {
              if (selected) {
                setSelectedChips([...selectedChips, 1]);
              } else {
                setSelectedChips(selectedChips.filter(id => id !== 1));
              }
            }}
          >
            Click to select
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.animation.title')}
        description={t('components.chips.animation.description')}
        code={`import { Chip } from '@/shared/components/common';

// Animated chip
<Chip animation>Animated</Chip>

// Animated chip with variant
<Chip animation variant="primary">
  Animated Primary
</Chip>`}
      >
        <div className="flex flex-wrap gap-2">
          <Chip animation>Animated</Chip>
          <Chip animation variant="primary">
            Animated Primary
          </Chip>
          <Chip animation variant="success">
            Animated Success
          </Chip>
        </div>
      </ComponentDemo>

      <ComponentDemo
        title={t('components.chips.group.title')}
        description={t('components.chips.group.description')}
        code={`import { ChipGroup } from '@/shared/components/common';
import { useState } from 'react';

// State for chips
const [chips, setChips] = useState([
  { id: 1, label: 'React', variant: 'primary' },
  { id: 2, label: 'Vue', variant: 'success' },
  { id: 3, label: 'Angular', variant: 'danger' },
  { id: 4, label: 'Svelte', variant: 'warning' },
]);

// State for selected chips
const [selectedChips, setSelectedChips] = useState([1]);

// Basic chip group
<ChipGroup
  chips={chips}
/>

// Closable chip group
<ChipGroup
  chips={chips}
  closable
  onDelete={(id) => setChips(chips.filter(chip => chip.id !== id))}
/>

// Multi-select chip group
<ChipGroup
  chips={chips}
  multiSelect
  selectedChips={selectedChips}
  onSelectionChange={setSelectedChips}
/>`}
      >
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium mb-2">
              {t('components.chips.group.basic', 'Basic Chip Group')}
            </h3>
            <ChipGroup chips={chips} />
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">
              {t('components.chips.group.closable', 'Closable Chip Group')}
            </h3>
            <ChipGroup
              chips={chips}
              closable
              onDelete={id => setChips(chips.filter(chip => chip.id !== id))}
            />
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">
              {t('components.chips.group.multiSelect', 'Multi-select Chip Group')}
            </h3>
            <ChipGroup
              chips={chips}
              multiSelect
              selectedChips={selectedChips}
              onSelectionChange={setSelectedChips}
            />
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">
              {t('components.chips.group.animated', 'Animated Chip Group')}
            </h3>
            <ChipGroup
              chips={chips}
              animation
              closable
              onDelete={id => setChips(chips.filter(chip => chip.id !== id))}
            />
          </div>
        </div>
      </ComponentDemo>
    </div>
  );
};

export default ChipsPage;
