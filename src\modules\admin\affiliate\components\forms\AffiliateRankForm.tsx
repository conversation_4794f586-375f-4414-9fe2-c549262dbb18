import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Card, Form, FormItem, Input, Button, Select, Textarea } from '@/shared/components/common';

import { AffiliateRankStatus } from '../../types/affiliate.types';

// Schema cho form
const formSchema = z.object({
  name: z.string().min(1, 'Tên cấp bậc là bắt buộc'),
  description: z.string().optional(),
  minReferrals: z.number().min(0, 'Số lượng giới thiệu tối thiểu không được âm'),
  commissionRate: z
    .number()
    .min(0, 'Tỷ lệ hoa hồng không được âm')
    .max(100, 'Tỷ lệ hoa hồng không được vượt quá 100%'),
  status: z.nativeEnum(AffiliateRankStatus, {
    errorMap: () => ({ message: 'Tr<PERSON>ng thái là bắt buộc' }),
  }),
});

export type AffiliateRankFormValues = z.infer<typeof formSchema>;

interface AffiliateRankFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  initialValues?: Partial<AffiliateRankFormValues>;
  isSubmitting?: boolean;
}

/**
 * Component form thêm/sửa cấp bậc affiliate
 */
const AffiliateRankForm: React.FC<AffiliateRankFormProps> = ({
  onSubmit,
  onCancel,
  initialValues,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['affiliate', 'common']);

  return (
    <Card className="mb-4 p-4">
      <Form
        schema={formSchema}
        onSubmit={onSubmit}
        className="space-y-4"
        defaultValues={initialValues}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label={t('affiliate:rank.form.name')} required>
            <Input placeholder={t('affiliate:rank.form.namePlaceholder')} fullWidth />
          </FormItem>

          <FormItem name="status" label={t('affiliate:rank.form.status')} required>
            <Select
              options={[
                { value: AffiliateRankStatus.ACTIVE, label: t('common:active') },
                { value: AffiliateRankStatus.INACTIVE, label: t('common:inactive') },
              ]}
              placeholder={t('affiliate:rank.form.statusPlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="minReferrals" label={t('affiliate:rank.form.minReferrals')} required>
            <Input
              type="number"
              placeholder={t('affiliate:rank.form.minReferralsPlaceholder')}
              fullWidth
            />
          </FormItem>

          <FormItem name="commissionRate" label={t('affiliate:rank.form.commissionRate')} required>
            <Input
              type="number"
              placeholder={t('affiliate:rank.form.commissionRatePlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="description" label={t('affiliate:rank.form.description')}>
          <Textarea
            placeholder={t('affiliate:rank.form.descriptionPlaceholder')}
            rows={3}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AffiliateRankForm;
