import React from 'react';
import { useTranslation } from 'react-i18next';

import { Icon, Select } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { useIsMobile } from '@/shared/hooks/common';
import { cn } from '@/shared/utils/cn';

interface ViewSelectProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

interface ViewOption {
  value: string;
  label: string;
  icon: IconName;
}

const ViewSelect: React.FC<ViewSelectProps> = ({ currentView, onViewChange }) => {
  const { t } = useTranslation();
  const isMobile = useIsMobile();

  // Define view options
  const viewOptions: ViewOption[] = [
    { value: 'dayGridMonth', label: t('calendar.month', 'Tháng'), icon: 'calendar' as IconName },
    { value: 'timeGridWeek', label: t('calendar.week', 'Tuần'), icon: 'layout' as IconName },
    { value: 'timeGridDay', label: t('calendar.day', 'Ngày'), icon: 'list' as IconName },
    { value: 'listWeek', label: t('calendar.list', 'Danh sách'), icon: 'document' as IconName },
  ];

  // Handle view change
  const handleViewChange = (view: string) => {
    onViewChange(view);
  };

  // Tạo options cho Select component với icon
  const selectOptions = viewOptions.map(option => ({
    value: option.value,
    label: option.label,
    icon: <Icon name={option.icon} size="sm" />,
  }));

  return (
    <Select
      value={currentView}
      onChange={value => handleViewChange(value as string)}
      options={selectOptions}
      size="sm"
      className={cn('view-select-dropdown', isMobile ? 'w-24' : 'w-32')}
      placeholder={isMobile ? '' : 'Chọn chế độ xem'}
    />
  );
};

export default ViewSelect;
