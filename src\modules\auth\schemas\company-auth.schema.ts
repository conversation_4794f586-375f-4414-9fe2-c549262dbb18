import { z } from 'zod';

/**
 * Schema cho form đăng ký công ty
 */
export const companyRegisterSchema = z.object({
  companyName: z
    .string()
    .min(3, { message: 'Tên công ty phải từ 3 đến 255 ký tự' })
    .max(255, { message: 'Tên công ty phải từ 3 đến 255 ký tự' }),
  companyEmail: z.string().email({ message: '<PERSON><PERSON> không hợp lệ' }),
  password: z
    .string()
    .min(8, { message: 'Mật khẩu phải từ 8 đến 50 ký tự' })
    .max(50, { message: 'Mật khẩu phải từ 8 đến 50 ký tự' })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!$%&*?@])[\d!$%&*?@A-Za-z]+$/, {
      message:
        '<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ cái viết thường, một chữ cái viết hoa, một chữ số và một ký tự đặc biệt',
    }),
  phoneNumber: z
    .string()
    .min(10, { message: 'Số điện thoại phải từ 10 đến 15 ký tự' })
    .max(15, { message: 'Số điện thoại phải từ 10 đến 15 ký tự' })
    .optional(),
  recaptchaToken: z.string().optional(),
});

/**
 * Schema cho form xác thực email
 */
export const companyVerifyEmailSchema = z.object({
  token: z.string().min(1, { message: 'Token không được để trống' }),
});

/**
 * Schema cho form đăng nhập công ty
 */
export const companyLoginSchema = z.object({
  email: z.string().email({ message: 'Email không hợp lệ' }),
  password: z.string().min(1, { message: 'Mật khẩu không được để trống' }),
});

/**
 * Type cho form đăng ký công ty
 */
export type CompanyRegisterFormValues = z.infer<typeof companyRegisterSchema>;

/**
 * Type cho form xác thực email
 */
export type CompanyVerifyEmailFormValues = z.infer<typeof companyVerifyEmailSchema>;

/**
 * Type cho form đăng nhập công ty
 */
export type CompanyLoginFormValues = z.infer<typeof companyLoginSchema>;
